/* src/index.css */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom Toast Styling for Better Text Visibility */
[data-sonner-toast] {
  background: #D32F2F !important;
  color: #FFFFFF !important;
  border: 1px solid #B71C1C !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 12px rgba(211, 47, 47, 0.3) !important;
}

[data-sonner-toast] [data-title] {
  color: #FFFFFF !important;
  font-weight: 600 !important;
}

[data-sonner-toast] [data-description] {
  color: #FFFFFF !important;
  opacity: 0.9 !important;
}

[data-sonner-toast] [data-icon] {
  color: #FFFFFF !important;
}

/* Success Toast Specific Styling */
[data-sonner-toast][data-type="success"] {
  background: #D32F2F !important;
  color: #FFFFFF !important;
}

/* Error Toast Specific Styling */
[data-sonner-toast][data-type="error"] {
  background: #B71C1C !important;
  color: #FFFFFF !important;
}

/* Info Toast Specific Styling */
[data-sonner-toast][data-type="info"] {
  background: #D9CAB3 !important;
  color: #2E2E2E !important;
}

[data-sonner-toast][data-type="info"] [data-title] {
  color: #2E2E2E !important;
}

[data-sonner-toast][data-type="info"] [data-description] {
  color: #2E2E2E !important;
  opacity: 0.8 !important;
}

html, body {
    width: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
  }