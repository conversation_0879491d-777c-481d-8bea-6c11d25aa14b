import React, { useState } from 'react'
import { Helmet } from 'react-helmet-async'
import SEO from '../shared/ui/SEO'

const SEOExample = () => {
  const [currentPage, setCurrentPage] = useState('home')

  // Example page data
  const pageData = {
    home: {
      title: 'Home',
      description: 'Welcome to ShringarHub - Your trusted destination for authentic religious and spiritual products.',
      keywords: 'religious products, spiritual items, hindu statues, poshak, brass items'
    },
    products: {
      title: 'Products',
      description: 'Browse our complete collection of authentic religious and spiritual products.',
      keywords: 'religious products, spiritual items, statues, jewelry, brass items'
    },
    about: {
      title: 'About Us',
      description: 'Learn about ShringarHub and our mission to provide authentic spiritual products.',
      keywords: 'about shringa<PERSON>hub, religious products company, spiritual items store'
    },
    contact: {
      title: 'Contact Us',
      description: 'Get in touch with ShringarHub for any questions about our products or services.',
      keywords: 'contact shringarhub, customer service, religious products support'
    }
  }

  const currentPageData = pageData[currentPage]

  return (
    <div className="min-h-screen bg-background p-8">
      {/* Using the reusable SEO component */}
      <SEO 
        title={currentPageData.title}
        description={currentPageData.description}
        keywords={currentPageData.keywords}
      />

      {/* Alternative: Using Helmet directly */}
      {/* 
      <Helmet>
        <title>{currentPageData.title} - ShringarHub</title>
        <meta name="description" content={currentPageData.description} />
        <meta name="keywords" content={currentPageData.keywords} />
      </Helmet>
      */}

      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold text-text mb-8 text-center">
          React Helmet SEO Example
        </h1>

        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-2xl font-semibold mb-4">Current Page SEO</h2>
          <div className="space-y-2">
            <p><strong>Title:</strong> {currentPageData.title} - ShringarHub</p>
            <p><strong>Description:</strong> {currentPageData.description}</p>
            <p><strong>Keywords:</strong> {currentPageData.keywords}</p>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-lg p-6">
          <h2 className="text-2xl font-semibold mb-4">Switch Pages</h2>
          <p className="text-gray-600 mb-4">
            Click the buttons below to see how the browser tab title changes dynamically:
          </p>
          
          <div className="flex flex-wrap gap-4">
            {Object.keys(pageData).map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                  currentPage === page
                    ? 'bg-primary text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {pageData[page].title}
              </button>
            ))}
          </div>

          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold mb-2">Instructions:</h3>
            <ol className="list-decimal list-inside space-y-1 text-sm text-gray-600">
              <li>Look at your browser tab title - it should show "{currentPageData.title} - ShringarHub"</li>
              <li>Click different page buttons above</li>
              <li>Watch the browser tab title update automatically</li>
              <li>Right-click and "View Page Source" to see the meta tags</li>
            </ol>
          </div>
        </div>
      </div>
    </div>
  )
}

export default SEOExample
