import React from 'react'
import { useLocation } from 'react-router-dom'
import { He<PERSON>etProvider } from 'react-helmet-async'
import { Toaster } from 'sonner'
import Header from './user/components/Header';
import CheckoutHeader from './user/components/checkout/CheckoutHeader';
import AppRoutes from './Routes';
import Footer from './user/components/Footer';


function App() {
  const location = useLocation()

  // Check if current route is checkout
  const isCheckoutPage = location.pathname === '/checkout'

  return (
    <HelmetProvider>
      <div className="flex flex-col min-h-screen bg-background">
      <Toaster
        position="top-right"
        toastOptions={{
          // Default styling for all toasts
          style: {
            background: '#D32F2F',
            color: '#FFFFFF',
            fontWeight: 500,
            boxShadow: '0 4px 12px rgba(211, 47, 47, 0.3)',
            borderRadius: '12px',
            border: '1px solid #B71C1C',
            fontSize: '14px',
            lineHeight: '1.5',
          },
          // Ensure description text is also white
          description: {
            color: '#FFFFFF',
            opacity: 0.9,
          },
          // Custom class for additional styling
          className: 'toast-custom',
          // Type-specific styling
          success: {
            style: {
              background: '#D32F2F',
              color: '#FFFFFF',
              fontWeight: 500,
              boxShadow: '0 4px 12px rgba(211, 47, 47, 0.3)',
              borderRadius: '12px',
              border: '1px solid #B71C1C',
            },
          },
          error: {
            style: {
              background: '#B71C1C',
              color: '#FFFFFF',
              fontWeight: 500,
              boxShadow: '0 4px 12px rgba(183, 28, 28, 0.4)',
              borderRadius: '12px',
              border: '1px solid #8B0000',
            },
          },
          info: {
            style: {
              background: '#D9CAB3',
              color: '#2E2E2E',
              fontWeight: 500,
              boxShadow: '0 4px 12px rgba(217, 202, 179, 0.3)',
              borderRadius: '12px',
              border: '1px solid #C6B499',
            },
          },
        }}
      />

      {/* Conditional Header Rendering */}
      {isCheckoutPage ? <CheckoutHeader /> : <Header />}

      <main className='flex-grow flex justify-center  my-4' >
        <AppRoutes/>
      </main>
      <Footer/>
      </div>
    </HelmetProvider>
  )
}

export default App
