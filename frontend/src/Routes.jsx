// Route.jsx
import React, { lazy, Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';

const Home = lazy(() => import('./user/pages/Home'));
const Login = lazy(() => import('./user/pages/Login'));
const Register = lazy(() => import('./user/pages/Register'))
const ForgotPassword = lazy(() => import('./user/pages/ForgotPassword'))
const Cart = lazy(() => import('./user/pages/Cart'))
const Products = lazy(() => import('./user/pages/Products'))
const ProductDetail = lazy(() => import('./user/pages/ProductDetail'))
const CategoryPage = lazy(() => import('./user/pages/CategoryPage'))
const Checkout = lazy(() => import('./user/pages/Checkout'))
const Account = lazy(() => import('./user/pages/Account'))
const AccountProfile = lazy(() => import('./user/pages/AccountProfile'))
const Orders = lazy(() => import('./user/pages/Orders'))
const ShippingAddress = lazy(() => import('./user/pages/ShippingAddress'))
const About = lazy(() => import('./user/pages/About'))
const Contact = lazy(() => import('./user/pages/Contact'))

const AppRoutes = () => (
    
    <Suspense fallback={<div className="text-center py-10">Loading...</div>}>
        <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/login" element={<Login />} />
            <Route path="/register" element={<Register/>}/>
            <Route path="/forgot-password" element={<ForgotPassword/>}/>
            <Route path="/cart" element={<Cart/>}/>
            <Route path="/checkout" element={<Checkout/>}/>
            <Route path="/products" element={<Products/>}/>
            <Route path="/products/:slug" element={<ProductDetail/>}/>
            <Route path="/category/:categoryName" element={<CategoryPage/>}/>
            <Route path="/about" element={<About/>}/>
            <Route path="/contact" element={<Contact/>}/>

            {/* Nested Account Routes */}
            <Route path="/account" element={<Account />}>
              <Route index  element={<AccountProfile />} />
              <Route path="profile" element={<AccountProfile />} />
              <Route path="orders" element={<Orders />} />
              <Route path="shipping-address" element={<ShippingAddress />} />
            </Route>
        </Routes>
    </Suspense>
);

export default AppRoutes;