import { useEffect } from 'react'
import useCartStore from '../store/cartStore'
import useAuthStore from '../store/authStore'

const useCartSync = () => {
  useEffect(() => {
    // Initialize cart sync with auth store
    const cartStore = useCartStore.getState()
    const authStore = useAuthStore.getState()
    
    // Register the cart sync function with auth store
    if (authStore.setSyncGuestCart && cartStore.syncGuestCart) {
      authStore.setSyncGuestCart(cartStore.syncGuestCart)
    }
    
    // Initialize auth state
    if (authStore.initializeAuth) {
      authStore.initializeAuth()
    }
  }, [])
}

export default useCartSync
