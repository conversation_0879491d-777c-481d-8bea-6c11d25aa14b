import dummyProducts from '../data/dummyProducts'

/**
 * Extract unique categories from dummyProducts
 * @returns {Array} Array of unique category names
 */
export const getUniqueCategories = () => {
  const categories = dummyProducts.map(product => product.category)
  return [...new Set(categories)].sort()
}

/**
 * Get products by category name
 * @param {string} categoryName - The category name to filter by
 * @returns {Array} Array of products in the specified category
 */
export const getProductsByCategory = (categoryName) => {
  if (!categoryName) return dummyProducts
  
  // Decode the category name from URL
  const decodedCategory = decodeURIComponent(categoryName)
  
  return dummyProducts.filter(product => 
    product.category.toLowerCase() === decodedCategory.toLowerCase()
  )
}

/**
 * Encode category name for URL
 * @param {string} categoryName - The category name to encode
 * @returns {string} URL-encoded category name
 */
export const encodeCategoryForURL = (categoryName) => {
  return encodeURIComponent(categoryName)
}

/**
 * Decode category name from URL
 * @param {string} encodedCategory - The encoded category name from URL
 * @returns {string} Decoded category name
 */
export const decodeCategoryFromURL = (encodedCategory) => {
  return decodeURIComponent(encodedCategory)
}

/**
 * Get category display name (for breadcrumbs, titles, etc.)
 * @param {string} categoryName - The category name
 * @returns {string} Formatted category display name
 */
export const getCategoryDisplayName = (categoryName) => {
  if (!categoryName) return 'All Categories'
  
  const decoded = decodeCategoryFromURL(categoryName)
  return decoded.charAt(0).toUpperCase() + decoded.slice(1)
}
