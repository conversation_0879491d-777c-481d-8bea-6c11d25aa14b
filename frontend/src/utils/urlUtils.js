// URL Utility Functions for SEO-friendly URLs

/**
 * Generate SEO-friendly slug from product name
 * @param {string} name - Product name
 * @returns {string} - SEO-friendly slug
 */
export const generateSlug = (name) => {
  if (!name) return ''
  
  return name
    .toLowerCase()
    .trim()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
}

/**
 * Generate complete product slug with ID
 * @param {object} product - Product object with name and id
 * @returns {string} - Complete slug with ID (e.g., "krishna-statue-brass-1")
 */
export const generateProductSlug = (product) => {
  if (!product || !product.name || !product.id) return ''
  
  const nameSlug = generateSlug(product.name)
  return `${nameSlug}-${product.id}`
}

/**
 * Extract product ID from slug
 * @param {string} slug - URL slug (e.g., "krishna-statue-brass-1")
 * @returns {number|null} - Product ID or null if invalid
 */
export const extractIdFromSlug = (slug) => {
  if (!slug) return null
  
  // Split by hyphen and get the last part
  const parts = slug.split('-')
  const lastPart = parts[parts.length - 1]
  
  // Check if last part is a valid number
  const id = parseInt(lastPart, 10)
  return isNaN(id) ? null : id
}

/**
 * Generate product URL path
 * @param {object} product - Product object
 * @returns {string} - URL path (e.g., "/products/krishna-statue-brass-1")
 */
export const generateProductUrl = (product) => {
  const slug = generateProductSlug(product)
  return slug ? `/products/${slug}` : `/products/${product.id}`
}

/**
 * Validate if current slug matches the product
 * @param {string} currentSlug - Current URL slug
 * @param {object} product - Product object
 * @returns {boolean} - True if slug matches product
 */
export const isValidSlug = (currentSlug, product) => {
  if (!currentSlug || !product) return false
  
  const correctSlug = generateProductSlug(product)
  return currentSlug === correctSlug
}

/**
 * Generate redirect URL if slug is incorrect
 * @param {string} currentSlug - Current URL slug
 * @param {object} product - Product object
 * @returns {string|null} - Redirect URL or null if no redirect needed
 */
export const getRedirectUrl = (currentSlug, product) => {
  if (!product) return null
  
  const correctSlug = generateProductSlug(product)
  
  // If current slug is just the ID, redirect to SEO-friendly URL
  if (currentSlug === product.id.toString()) {
    return `/products/${correctSlug}`
  }
  
  // If slug doesn't match, redirect to correct slug
  if (!isValidSlug(currentSlug, product)) {
    return `/products/${correctSlug}`
  }
  
  return null
}

/**
 * Parse slug and return both ID and slug validation info
 * @param {string} slug - URL slug
 * @returns {object} - Object with id, isValid, and other info
 */
export const parseSlug = (slug) => {
  const id = extractIdFromSlug(slug)
  
  return {
    id,
    isValid: id !== null,
    originalSlug: slug,
    isNumericOnly: /^\d+$/.test(slug) // Check if slug is just a number (old format)
  }
}

// Example usage:
// generateProductSlug({ name: "Krishna Statue - Brass Handcrafted", id: 1 })
// Returns: "krishna-statue-brass-handcrafted-1"

// extractIdFromSlug("krishna-statue-brass-handcrafted-1")
// Returns: 1

// generateProductUrl({ name: "Krishna Statue", id: 1 })
// Returns: "/products/krishna-statue-1"
