// Test file to verify category utilities
import { getUniqueCategories, getProductsByCategory, encodeCategoryForURL, decodeCategoryFromURL } from './categoryUtils'

// Test the utilities
console.log('=== Category Utils Test ===')

// Test 1: Get unique categories
const categories = getUniqueCategories()
console.log('Unique categories:', categories)

// Test 2: Test URL encoding/decoding
const testCategories = ['Brass Items', 'Jwellery Set', 'Statue']
testCategories.forEach(category => {
  const encoded = encodeCategoryForURL(category)
  const decoded = decodeCategoryFromURL(encoded)
  console.log(`Original: "${category}" -> Encoded: "${encoded}" -> Decoded: "${decoded}"`)
})

// Test 3: Get products by category
categories.forEach(category => {
  const products = getProductsByCategory(category)
  console.log(`Category "${category}": ${products.length} products`)
})

// Test 4: Test with encoded category names
const encodedBrassItems = encodeCategoryForURL('Brass Items')
const brassProducts = getProductsByCategory(encodedBrassItems)
console.log(`Brass Items (encoded): ${brassProducts.length} products`)

export { categories }
