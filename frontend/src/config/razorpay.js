// Razorpay Configuration
// Uses environment variables to keep keys secure

export const RAZORPAY_CONFIG = {
  // Get keys from environment variables
  TEST_KEY: import.meta.env.VITE_RAZORPAY_TEST_KEY || '',
  LIVE_KEY: import.meta.env.VITE_RAZORPAY_LIVE_KEY || '',

  // Determine environment
  IS_PRODUCTION: import.meta.env.VITE_APP_ENV === 'production',

  // Get the appropriate key based on environment
  getKey: () => {
    const key = RAZORPAY_CONFIG.IS_PRODUCTION
      ? RAZORPAY_CONFIG.LIVE_KEY
      : RAZORPAY_CONFIG.TEST_KEY

    if (!key) {
      console.error('Razorpay key not found in environment variables')
      throw new Error('Razorpay key not configured')
    }

    return key
  },
  
  // Default options
  DEFAULT_OPTIONS: {
    currency: 'INR',
    theme: {
      color: '#D32F2F'
    },
    modal: {
      escape: true,
      backdropclose: false
    }
  }
}

// Instructions for getting your Razorpay key:
// 1. Go to https://razorpay.com/
// 2. Sign up for a free account
// 3. Go to Dashboard → Settings → API Keys
// 4. Generate Test Key ID
// 5. Replace the TEST_KEY above with your actual key
// 6. The key format should be: rzp_test_xxxxxxxxxx

export default RAZORPAY_CONFIG
