import React from 'react'
import { useNavigate } from 'react-router-dom'
import { RxCross2 } from 'react-icons/rx'
import { FaUser, FaShoppingCart } from 'react-icons/fa'
import Button from './Button'
import Modal from './Modal'

const LoginPromptModal = ({ isOpen, onClose, action = 'access cart' }) => {
  const navigate = useNavigate()

  const handleLogin = () => {
    onClose()
    navigate('/login')
  }

  const handleRegister = () => {
    onClose()
    navigate('/register')
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} maxWidth="max-w-md">
      <div className="relative p-6 text-center">
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-0 right-2 p-1 hover:bg-gray-100 rounded-full transition-colors"
        >
          <RxCross2 className="w-6 h-6 text-gray-500 hover:text-gray-700" />
        </button>

        {/* Icon */}
        <div className="mx-auto flex items-center justify-center w-16 h-16 bg-primary/10 rounded-full mb-4">
          <FaUser className="w-8 h-8 text-primary" />
        </div>

        {/* Title */}
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          Login Required
        </h2>

        {/* Message */}
        <p className="text-gray-600 mb-6">
          Please log in to {action}. Don't have an account? Create one to get started!
        </p>

        {/* Buttons */}
        <div className="space-y-3">
          <Button
            onClick={handleLogin}
            variant="primary"
            className="w-full"
          >
            Log In
          </Button>

          <Button
            onClick={handleRegister}
            variant="outline"
            className="w-full"
          >
            Create Account
          </Button>
        </div>

        {/* Note */}
        <p className="text-xs text-gray-500 mt-4">
          <FaShoppingCart className="inline w-3 h-3 mr-1" />
          Your cart items are saved and will be available when you log in
        </p>
      </div>
    </Modal>
  )
}

export default LoginPromptModal
