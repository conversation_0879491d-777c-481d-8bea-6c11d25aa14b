import React from 'react'
import { Helmet } from 'react-helmet-async'

const SEO = ({
  title,
  description,
  keywords,
  image,
  url,
  type = 'website',
  siteName = 'ShringarHub'
}) => {
  // Format title with site name
  const formattedTitle = title ? `${title} - ${siteName}` : siteName

  // Default meta description
  const defaultDescription = 'ShringarHub - Your trusted destination for authentic religious and spiritual products. Discover beautiful statues, traditional poshaks, brass items, jewelry, and more sacred items for your spiritual journey.'

  // Default keywords
  const defaultKeywords = 'religious products, spiritual items, statues, poshak, brass items, jewelry, divine products, hindu religious items, spiritual accessories, temple items'

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{formattedTitle}</title>
      <meta name="description" content={description || defaultDescription} />
      <meta name="keywords" content={keywords || defaultKeywords} />
      
      {/* Open Graph Meta Tags */}
      <meta property="og:title" content={formattedTitle} />
      <meta property="og:description" content={description || defaultDescription} />
      <meta property="og:type" content={type} />
      <meta property="og:site_name" content={siteName} />
      {url && <meta property="og:url" content={url} />}
      {image && <meta property="og:image" content={image} />}
      
      {/* Twitter Card Meta Tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={formattedTitle} />
      <meta name="twitter:description" content={description || defaultDescription} />
      {image && <meta name="twitter:image" content={image} />}
      
      {/* Additional Meta Tags */}
      <meta name="robots" content="index, follow" />
      <meta name="author" content="ShringarHub" />
      <meta name="viewport" content="width=device-width, initial-scale=1.0" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      
      {/* Canonical URL */}
      {url && <link rel="canonical" href={url} />}
    </Helmet>
  )
}

export default SEO
