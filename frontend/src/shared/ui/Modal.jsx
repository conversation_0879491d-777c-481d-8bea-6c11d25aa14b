import React from 'react'
import { RxCross2 } from 'react-icons/rx'

const Modal = ({ isOpen, onClose, title, children, maxWidth = 'max-w-2xl' }) => {
  if (!isOpen) return null

  const handleBackdropClick = (e) => {
    // Only close if clicking directly on the backdrop, not on any child elements
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  const handleModalContentClick = (e) => {
    // Prevent event bubbling to backdrop
    e.stopPropagation()
  }

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50"
      onClick={handleBackdropClick}
    >
      <div
        className={`bg-white rounded-lg w-full ${maxWidth} shadow-lg p-0 m-2 max-h-[90vh] overflow-y-auto`}
        onClick={handleModalContentClick}
      >
        {/* Header */}
        <div className="flex justify-between items-center border-b px-6 py-4">
          <h2 className="text-lg font-semibold text-center w-full">{title}</h2>
          <button 
            onClick={onClose} 
            className="absolute right-6 hover:bg-gray-100 rounded-full p-1 transition-colors"
          >
            <RxCross2 className="w-5 h-5 text-gray-500 hover:text-black" />
          </button>
        </div>
        
        {/* Content */}
        <div className="px-6 py-4">
          {children}
        </div>
      </div>
    </div>
  )
}

export default Modal
