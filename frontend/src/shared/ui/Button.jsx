import React from 'react'

const Button = ({
    children, 
    type='button', 
    variant='primary', 
    size= 'md', 
    onClick, 
    className = '',
    disabled = false,
    
    
}) => {

    const base = 'font-medium transition duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 ';

    const variants = {
        primary: 'bg-red-600 hover:bg-red-700 text-white focus:ring-red-600',
        secondary: 'bg-[#D9CAB3] hover:bg-camel-600 text-black',
        danger: 'bg-red-500 text-white hover:bg-red-700',
        outline: 'border border-[#B71C1C] border-2 text-[#B71C1C] hover:bg-[#B71C1C] hover:text-white',
    };

    const sizes = {
        sm: 'px-3 py-1 text-sm',
        md: 'px-4 py-2 text-base' ,
        lg: 'px-5 py-3 text-lg'
    };

  return (
    <button
        type= {type}
        onClick= {onClick}
        disabled = {disabled}
        className = {`
            ${base} 
            ${variants[variant]}
            ${sizes[size]}
            ${className}
            ${disabled  ? 'opacity-50 cursor-not-allowed' : ''}
        `}
    >
        {children}
    </button>
  )
}

export default Button
