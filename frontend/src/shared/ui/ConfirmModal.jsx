import React from 'react'
import { FaExclamationTriangle } from 'react-icons/fa'
import Modal from './Modal'
import Button from './Button'

const ConfirmModal = ({ 
  isOpen, 
  onClose, 
  onConfirm, 
  title = 'Confirm Action',
  message = 'Are you sure you want to proceed?',
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmVariant = 'primary',
  icon = 'warning' // 'warning', 'danger', 'info'
}) => {
  const handleConfirm = () => {
    onConfirm()
    onClose()
  }

  const handleCancel = () => {
    onClose()
  }

  const getIcon = () => {
    switch (icon) {
      case 'danger':
        return <FaExclamationTriangle className="w-6 h-6 text-red-500" />
      case 'warning':
        return <FaExclamationTriangle className="w-6 h-6 text-yellow-500" />
      case 'info':
        return <FaExclamationTriangle className="w-6 h-6 text-blue-500" />
      default:
        return <FaExclamationTriangle className="w-6 h-6 text-yellow-500" />
    }
  }

  const getIconBgColor = () => {
    switch (icon) {
      case 'danger':
        return 'bg-red-100'
      case 'warning':
        return 'bg-yellow-100'
      case 'info':
        return 'bg-blue-100'
      default:
        return 'bg-yellow-100'
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={onClose} maxWidth="max-w-md">
      <div className="p-6">
        {/* Icon */}
        <div className={`mx-auto flex items-center justify-center w-12 h-12 ${getIconBgColor()} rounded-full mb-4`}>
          {getIcon()}
        </div>

        {/* Title */}
        <h3 className="text-lg font-semibold text-gray-900 text-center mb-2">
          {title}
        </h3>

        {/* Message */}
        <p className="text-gray-600 text-center mb-6">
          {message}
        </p>

        {/* Buttons */}
        <div className="flex space-x-3">
          <Button
            onClick={handleCancel}
            variant="outline"
            className="flex-1"
          >
            {cancelText}
          </Button>
          <Button
            onClick={handleConfirm}
            variant={confirmVariant}
            className="flex-1"
          >
            {confirmText}
          </Button>
        </div>
      </div>
    </Modal>
  )
}

export default ConfirmModal
