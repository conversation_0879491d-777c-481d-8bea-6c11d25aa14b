import React from 'react'

const Textarea = ({
  label,
  name,
  value,
  onChange,
  placeholder = '',
  className = '',
  required = false,
  disabled = false,
  error = '',
  rows = 4,
  ...rest
}) => {
  return (
    <div className="flex flex-col gap-2">
      {label && (
        <label
          htmlFor={name}
          className="text-sm font-medium text-gray-700"
        >
          {label}
          {required && <span className="text-primary ml-0.5">*</span>}
        </label>
      )}
      <textarea
        id={name}
        name={name}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        disabled={disabled}
        rows={rows}
        className={`
          flex-1 px-4 py-2 border rounded-md border-gray-300
          text-black resize-vertical
          focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent
          ${disabled ? 'bg-gray-100 cursor-not-allowed text-gray-500' : 'bg-white'}
          ${error ? 'border-red-500' : ''}
          ${className}
        `}
        {...rest}
      />
      {error && <p className="text-xs text-red-500">{error}</p>}
    </div>
  )
}

export default Textarea
