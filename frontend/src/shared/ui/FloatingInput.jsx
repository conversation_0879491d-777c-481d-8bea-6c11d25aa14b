import React, { useState } from 'react';

const FloatingInput = ({
  type = 'text',
  name,
  value,
  onChange,
  placeholder = '',
  className = '',
  required = false,
  disabled = false,
  error = '',
  ...rest
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const shouldFloat = isFocused || value;

  return (
    <div className={`relative w-full ${className}`}>
      {/* Floating Label */}
      <label
        htmlFor={name}
        className={`
          absolute left-3 px-1 transition-all duration-200 bg-white 
          ${shouldFloat ? 'top-[-0.6rem] text-xs text-black' : 'top-1/2 text-sm text-gray-500 transform -translate-y-1/2 '}
          pointer-events-none
        `}
      >
        {placeholder}
      </label>

      <input
        id={name}
        name={name}
        type={type}
        value={value}
        onChange={onChange}
        required={required}
        disabled={disabled}
        onFocus={() => setIsFocused(true)}
        onBlur={() => setIsFocused(false)}
        className={`
          w-full px-3 pt-5 pb-2 border rounded-md border-gray-300 
          focus:outline-none focus:ring-0 focus:border-black focus:primary focus:border-transparent
          ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
          ${error ? 'border-red-500' : ''}
        `}
        {...rest}
      />

      {error && <p className="text-xs text-red-500 mt-1">{error}</p>}
    </div>
  );
};

export default FloatingInput;
