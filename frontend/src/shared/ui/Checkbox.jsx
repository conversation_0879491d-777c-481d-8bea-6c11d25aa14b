import React from "react";

const Checkbox = ({ checked, onChange, label, id }) => {
  return (
    <label htmlFor={id} className="flex items-center space-x-2 cursor-pointer">
      <input
        id={id}
        type="checkbox"
        checked={checked}
        onChange={onChange}
        className="hidden peer"
      />
      <div className="w-5 h-5 rounded border border-gray-400 flex items-center justify-center peer-checked:bg-black peer-checked:border-black">
        <svg
          className="w-3 h-3 text-white hidden peer-checked:block"
          fill="none"
          stroke="currentColor"
          strokeWidth="3"
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
        </svg>
      </div>
      {label && <span className="text-gray-700">{label}</span>}
    </label>
  );
};

export default Checkbox;
