import React, {useState} from 'react'
import Input from '../../shared/ui/Input'
import Button from '../../shared/ui/Button'
import LinkText from '../../shared/ui/LinkText'
import { toast } from 'sonner';
import { Link, useNavigate } from 'react-router-dom';
import { AiOutlineEye, AiOutlineEyeInvisible, AiOutlineLoading3Quarters } from "react-icons/ai";
import useAuthStore from "../../store/authStore";
import logo from '../../assets/Logo1.png';
import SEO from '../../shared/ui/SEO';

const Register = () => {
    const navigate = useNavigate();
    const { register, isLoading, error, clearError } = useAuthStore()

    const [formData, setFormData] = useState({
        firstName: '',
        lastName: '',
        email: '',
        password: '',
        confirmPassword: '',
        acceptTerms: false,
        subscribeNewsletter: false
    })
    const [show, setShow] = useState(false);
    const [errors, setErrors] = useState({});

    const handleChange = (e) => {
        setFormData((prev) => ({
            ...prev,
            [e.target.name] : e.target.value,
        }));  

        setErrors((prev) => ({
            ...prev,
            [e.target.name]: '', // Clear error when user starts typing
        }));
    };

    const validateForm = (formData) => {
        const errors = {};

        // First name validation
        if (!formData.firstName.trim()) {
            errors.firstName = 'First name is required';
        } else if (formData.firstName.trim().length < 2) {
            errors.firstName = 'First name must be at least 2 characters';
        }

        // Last name validation
        if (!formData.lastName.trim()) {
            errors.lastName = 'Last name is required';
        } else if (formData.lastName.trim().length < 2) {
            errors.lastName = 'Last name must be at least 2 characters';
        }

        // Email validation
        if (!formData.email.trim()) {
            errors.email = "Email is required";
        } else if (!/^\S+@\S+\.\S+$/.test(formData.email)) {
            errors.email = "Enter a valid email address";
        }

        // Password validation (single-error style)
        if (!formData.password.trim()) {
            errors.password = "Password is required";
        } else if (formData.password.length < 6) {
            errors.password = "Password must be at least 6 characters";
        } else if (!/[A-Z]/.test(formData.password)) {
            errors.password = "Include at least one uppercase letter";
        } else if (!/[0-9]/.test(formData.password)) {
            errors.password = "Include at least one number";
        } else if (!/[!@#$%^&*(),.?":{}|<>]/.test(formData.password)) {
            errors.password = "Include at least one special character";
        } else if (/\s/.test(formData.password)) {
            errors.password = "Password cannot contain spaces";
        }

        // Confirm password validation
        if (!formData.confirmPassword.trim()) {
            errors.confirmPassword = "Please confirm your password";
        } else if (formData.password !== formData.confirmPassword) {
            errors.confirmPassword = "Passwords do not match";
        }

        // Terms acceptance validation
        if (!formData.acceptTerms) {
            errors.acceptTerms = "You must accept the terms and conditions";
        }

        return errors;
    };

    const handleSubmit = async(e) => {
        e.preventDefault();

        // Clear any previous errors
        clearError();

        const validationErrors = validateForm(formData);

        if (Object.keys(validationErrors).length > 0) {
          setErrors(validationErrors);
          toast.error('Please correct the highlighted errors.');
          return;
        }
        setErrors({}); // Clear errors

        try {
            const fullName = `${formData.firstName} ${formData.lastName}`;
            const result = await register({
                name: fullName,
                email: formData.email,
                subscribeNewsletter: formData.subscribeNewsletter
                // Password removed - will be added when backend is ready
            })

            if (result.success) {
                // Show single success toast message
                toast.success('Registration successful! Please log in with your credentials.');

                // Redirect to login page with email pre-filled
                navigate('/login', {
                    state: {
                        email: formData.email
                    }
                });
            } else {
                toast.error(result.error || 'Registration failed');
            }
        } catch (error) {
            toast.error(error.message || 'Registration failed');
        }

        //clear form fields
        setFormData({
            firstName: '',
            lastName: '',
            email: '',
            password: '',
            confirmPassword: '',
            acceptTerms: false,
            subscribeNewsletter: false
        });
    };
    

  return (
    <div className="min-h-screen">
      <SEO
        title="Register"
        description="Create your ShringarHub account to start shopping for authentic religious and spiritual products. Join our community and enjoy personalized shopping experience."
        keywords="register, sign up, create account, new account, shringarhub registration, religious products account"
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center">
          <div className="w-full max-w-4xl">
            <div className="bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden">

              {/* Header */}
              <div className="bg-white p-8 text-center">
                <Link to="/" className="inline-block mb-6">
                  <img
                    src={logo}
                    alt="ShringarHub"
                    className="w-20 h-20 object-contain rounded-full mx-auto hover:scale-105 transition-transform duration-200"
                  />
                </Link>
                <h1 className="text-3xl font-bold text-gray-900 mb-3">Create Account</h1>
                <p className="text-gray-600 leading-relaxed">
                  Join ShringarHub and start your divine shopping journey
                </p>
              </div>

          {/* Form */}
          <div className="px-8 sm:px-16 lg:px-24 pb-8 sm:pb-16">
            <form onSubmit={handleSubmit} className="space-y-8" noValidate>
              {/* Name Fields */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <Input
                  label="First Name"
                  type="text"
                  name="firstName"
                  placeholder="First name"
                  value={formData.firstName}
                  onChange={handleChange}
                  error={errors.firstName}
                  autoComplete="given-name"
                />
                <Input
                  label="Last Name"
                  type="text"
                  name="lastName"
                  placeholder="Last name"
                  value={formData.lastName}
                  onChange={handleChange}
                  error={errors.lastName}
                  autoComplete="family-name"
                />
              </div>

              {/* Email Input */}
              <Input
                label="Email Address"
                type="email"
                name="email"
                placeholder="Enter your email"
                value={formData.email}
                onChange={handleChange}
                error={errors.email}
                autoComplete="email"
              />

              {/* Password Input */}
              <div className="relative">
                <Input
                  label="Password"
                  placeholder="Create a strong password"
                  type={show ? "text" : "password"}
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  error={errors.password}
                  autoComplete="new-password"
                />
                <button
                  type="button"
                  onClick={() => setShow(!show)}
                  className="absolute right-4 top-[40px] text-gray-500 hover:text-gray-700"
                  aria-label={show ? "Hide password" : "Show password"}
                >
                  {show ? <AiOutlineEyeInvisible /> : <AiOutlineEye />}
                </button>
              </div>

              {/* Confirm Password Input */}
              <Input
                label="Confirm Password"
                placeholder="Confirm your password"
                type="password"
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChange}
                error={errors.confirmPassword}
                autoComplete="new-password"
              />

              {/* Terms and Conditions */}
              <div className="space-y-4">
                <div className="flex items-start gap-3">
                  <input
                    type="checkbox"
                    name="acceptTerms"
                    checked={formData.acceptTerms}
                    onChange={handleChange}
                    className="w-4 h-4 mt-1 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2"
                  />
                  <label className="text-sm text-gray-600 leading-relaxed">
                    I agree to the{" "}
                    <Link to="/terms" className="text-primary hover:underline font-medium">
                      Terms and Conditions
                    </Link>{" "}
                    and{" "}
                    <Link to="/privacy" className="text-primary hover:underline font-medium">
                      Privacy Policy
                    </Link>
                  </label>
                </div>
                {errors.acceptTerms && (
                  <p className="text-red-500 text-sm">{errors.acceptTerms}</p>
                )}

                {/* Newsletter Subscription */}
                <div className="flex items-start gap-3">
                  <input
                    type="checkbox"
                    name="subscribeNewsletter"
                    checked={formData.subscribeNewsletter}
                    onChange={handleChange}
                    className="w-4 h-4 mt-1 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2"
                  />
                  <label className="text-sm text-gray-600 leading-relaxed">
                    Subscribe to our newsletter for updates and special offers
                  </label>
                </div>
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                disabled={isLoading}
                variant="primary"
                className="w-full py-4 rounded-xl text-lg font-semibold transition-all duration-300 hover:shadow-lg"
              >
                {isLoading && (
                  <AiOutlineLoading3Quarters className="animate-spin mr-2 inline-block" />
                )}
                {isLoading ? "Creating Account..." : "Create Account"}
              </Button>

              {/* Login Link */}
              <div className="text-center pt-6 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  Already have an account?{" "}
                  <LinkText to="/login">Sign In</LinkText>
                </p>
              </div>
            </form>
          </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Register
