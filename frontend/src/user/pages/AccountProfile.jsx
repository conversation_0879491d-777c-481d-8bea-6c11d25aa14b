import React, { useState, useRef } from 'react'
import { FaUser, FaEdit, FaSave, FaTimes, FaCamera, FaTrash } from 'react-icons/fa'
import { toast } from 'sonner'
import SEO from '../../shared/ui/SEO'
import useAuthStore from '../../store/authStore'

const AccountProfile = () => {
  const { currentUser, updateUser } = useAuthStore()
  const [isEditing, setIsEditing] = useState(false)
  const [formData, setFormData] = useState({
    name: currentUser?.name || '',
    email: currentUser?.email || '',
    phone: currentUser?.phone || ''
  })
  const [profileImage, setProfileImage] = useState(currentUser?.profileImage || null)
  const [imagePreview, setImagePreview] = useState(currentUser?.profileImage || null)
  const fileInputRef = useRef(null)

  // Image handling functions
  const handleImageUpload = (event) => {
    const file = event.target.files[0]
    if (file) {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast.error('Please select a valid image file')
        return
      }

      // Validate file size (max 5MB)
      if (file.size > 5 * 1024 * 1024) {
        toast.error('Image size should be less than 5MB')
        return
      }

      // Create preview URL
      const reader = new FileReader()
      reader.onload = (e) => {
        setImagePreview(e.target.result)
        setProfileImage(e.target.result)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleRemoveImage = () => {
    setProfileImage(null)
    setImagePreview(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const triggerFileInput = () => {
    fileInputRef.current?.click()
  }

  const handleEdit = () => {
    setIsEditing(true)
    setFormData({
      name: currentUser?.name || '',
      email: currentUser?.email || '',
      phone: currentUser?.phone || ''
    })
    setImagePreview(currentUser?.profileImage || null)
  }

  const handleSave = () => {
    // In a real app, you would make an API call here
    const updatedData = {
      ...formData,
      profileImage: profileImage
    }
    updateUser(updatedData)
    setIsEditing(false)
    toast.success('Profile updated successfully')
  }

  const handleCancel = () => {
    setIsEditing(false)
    setFormData({
      name: currentUser?.name || '',
      email: currentUser?.email || '',
      phone: currentUser?.phone || ''
    })
    setImagePreview(currentUser?.profileImage || null)
    setProfileImage(currentUser?.profileImage || null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  return (
    <div>
      <SEO
        title="Personal Information"
        description="Manage your personal information and account details at ShringarHub. Update your profile, contact information, and preferences."
        keywords="personal information, account details, profile settings, user profile, account management, update profile"
      />

      <div className="flex items-center justify-between mb-8">
        <div className="flex items-center space-x-3">
          <FaUser className="w-6 h-6 text-primary" />
          <h2 className="text-2xl font-semibold text-black">Personal Information</h2>
        </div>
        {!isEditing && (
          <button
            onClick={handleEdit}
            className="flex items-center space-x-2 text-primary hover:text-primary-dark underline transition-colors"
          >
            <FaEdit className="w-4 h-4" />
            <span>Edit</span>
          </button>
        )}
      </div>

      <div className="space-y-6">
        
        {/* Profile Picture Section */}
        <div className="flex items-center space-x-6 pb-6 border-b border-gray-200">
          <div className="relative">
            <div className="w-24 h-24 rounded-full overflow-hidden bg-primary bg-opacity-10 flex items-center justify-center">
              {imagePreview ? (
                <img
                  src={imagePreview}
                  alt="Profile"
                  className="w-full h-full object-cover"
                />
              ) : (
                <FaUser className="w-10 h-10 text-primary" />
              )}
            </div>
            {isEditing && (
              <div className="absolute -bottom-2 -right-2 flex space-x-1">
                <button
                  onClick={triggerFileInput}
                  className="bg-primary text-white p-2 rounded-full hover:bg-primary-dark transition-colors shadow-lg"
                  title="Upload Photo"
                >
                  <FaCamera className="w-3 h-3" />
                </button>
                {imagePreview && (
                  <button
                    onClick={handleRemoveImage}
                    className="bg-red-500 text-white p-2 rounded-full hover:bg-red-600 transition-colors shadow-lg"
                    title="Remove Photo"
                  >
                    <FaTrash className="w-3 h-3" />
                  </button>
                )}
              </div>
            )}
          </div>
          <div>
            <h3 className="text-lg font-medium text-black">Profile Picture</h3>
            <p className="text-gray-600 text-sm mb-3">Upload a profile picture to personalize your account</p>
            <div className="text-xs text-gray-500">
              <p>• Supported formats: JPG, PNG, GIF</p>
              <p>• Maximum size: 5MB</p>
            </div>
            {!isEditing && !imagePreview && (
              <button
                onClick={() => setIsEditing(true)}
                className="mt-3 text-primary hover:text-primary-dark underline text-sm"
              >
                Upload Photo
              </button>
            )}
          </div>

          {/* Hidden file input */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleImageUpload}
            className="hidden"
          />
        </div>

        {/* Personal Details */}
        <div className="space-y-6">
          
          {/* Name */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
            {isEditing ? (
              <input
                type="text"
                value={formData.name}
                onChange={(e) => handleChange('name', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                placeholder="Enter your full name"
              />
            ) : (
              <div className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg">
                <span className="text-black">{currentUser?.name || 'Not provided'}</span>
              </div>
            )}
          </div>

          {/* Email */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
            {isEditing ? (
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleChange('email', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                placeholder="Enter your email address"
              />
            ) : (
              <div className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg">
                <span className="text-black">{currentUser?.email || 'Not provided'}</span>
              </div>
            )}
          </div>

          {/* Phone */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
            {isEditing ? (
              <input
                type="tel"
                value={formData.phone}
                onChange={(e) => handleChange('phone', e.target.value)}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary"
                placeholder="Enter your phone number"
              />
            ) : (
              <div className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg">
                <span className="text-black">{currentUser?.phone || 'Not provided'}</span>
              </div>
            )}
          </div>

          {/* Member Since */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Member Since</label>
            <div className="px-4 py-3 bg-gray-50 border border-gray-200 rounded-lg">
              <span className="text-black">
                {currentUser?.joinDate 
                  ? new Date(currentUser.joinDate).toLocaleDateString('en-US', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric'
                    })
                  : 'Recently joined'
                }
              </span>
            </div>
          </div>

        </div>

        {/* Action Buttons */}
        {isEditing && (
          <div className="flex space-x-4 pt-6 border-t border-gray-200">
            <button
              onClick={handleSave}
              className="flex items-center space-x-2 px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
            >
              <FaSave className="w-4 h-4" />
              <span>Save Changes</span>
            </button>
            <button
              onClick={handleCancel}
              className="flex items-center space-x-2 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <FaTimes className="w-4 h-4" />
              <span>Cancel</span>
            </button>
          </div>
        )}

        {/* Additional Options */}
        <div className="pt-6 border-t border-gray-200">
          <h3 className="text-lg font-medium text-black mb-4">Account Settings</h3>
          <div className="space-y-3">
            <button className="text-primary hover:text-primary-dark underline text-sm">
              Change Password
            </button>
            <br />
            <button className="text-red-600 hover:text-red-700 underline text-sm">
              Delete Account
            </button>
          </div>
        </div>

      </div>
    </div>
  )
}

export default AccountProfile
