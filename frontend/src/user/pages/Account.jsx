import React, { useEffect } from 'react'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import { <PERSON>a<PERSON><PERSON>, FaClipboardList,FaMapMarkerAlt, FaSignOutAlt } from 'react-icons/fa'
import { toast } from 'sonner'
import useAuthStore from '../../store/authStore'

const Account = () => {
  const navigate = useNavigate()
  const location = useLocation()
  const { logout, currentUser } = useAuthStore()

  // Redirect /account to /account/profile
  useEffect(() => {
    if (location.pathname === '/account') {
      navigate('/account/profile', { replace: true })
    }
  }, [location.pathname, navigate])

  const handleLogout = () => {
    logout()
    toast.success('Logged out successfully')
    navigate('/')
  }

  // Navigation items for account section
  const accountNavItems = [
    {
      path: '/account/profile',
      label: 'Profile',
      icon: FaUser,
      exact: true
    },
    {
      path: '/account/orders',
      label: 'Orders',
      icon: FaClipboardList,
      exact: false
    },
    {
      path: '/account/shipping-address',
      label: 'Shipping Address',
      icon: FaMapMarkerAlt,
      exact: false
    }
  ]

  const isActiveRoute = (path, exact) => {
    if (exact) {
      return location.pathname === path
    }
    return location.pathname.startsWith(path)
  }

  return (
    <div className="min-h-screen bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <button onClick={() => navigate('/')} className="hover:text-black transition-colors">
            Home
          </button>
          <span>/</span>
          <span className="text-black font-medium">My Account</span>
        </nav>

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-12 pb-8 border-b border-gray-200">
          <div>
            <h1 className="text-3xl font-normal text-black mb-2">My Account</h1>
            <p className="text-gray-600 text-lg">
              Hello, {currentUser?.name || currentUser?.email?.split('@')[0] || 'User'}!
            </p>
          </div>
          <button
            onClick={handleLogout}
            className="flex items-center space-x-2 text-red-600 hover:text-red-700 underline transition-colors mt-4 sm:mt-0"
          >
            <FaSignOutAlt className="w-4 h-4" />
            <span>Sign Out</span>
          </button>
        </div>

        {/* Account Navigation & Content */}
        <div className="grid grid-cols-1 lg:grid-cols-5 gap-8">

          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-black mb-6">Account Menu</h2>
              <nav className="space-y-2">
                {accountNavItems.map((item) => {
                  const Icon = item.icon
                  const isActive = isActiveRoute(item.path, item.exact)

                  return (
                    <button
                      key={item.path}
                      onClick={() => navigate(item.path)}
                      className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                        isActive
                          ? 'bg-primary text-white'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-primary'
                      }`}
                    >
                      <Icon className="w-5 h-5" />
                      <span>{item.label}</span>
                    </button>
                  )
                })}
              </nav>
            </div>
          </div>

          {/* Main Content Area */}
          <div className="lg:col-span-4">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <Outlet />
            </div>
          </div>

        </div>
      </div>
    </div>
  )
}

export default Account
