import React, { useEffect, useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { FaArrowLeft } from 'react-icons/fa'
import { toast } from 'sonner'
import useAuthStore from '../../store/authStore'
import useCartStore from '../../store/cartStore'
import useCheckoutStore from '../../store/checkoutStore'
import useAddressStore from '../../store/addressStore'
import SEO from '../../shared/ui/SEO'

import AddressSelector from '../components/checkout/AddressSelector'
import BillingAddressSelector from '../components/checkout/BillingAddressSelector'
import PaymentOptions from '../components/checkout/PaymentOptions'
import OrderSummary from '../components/checkout/OrderSummary'
import LoginPromptModal from '../../shared/ui/LoginPromptModal'

const Checkout = () => {
  const navigate = useNavigate()
  const { isAuthenticated, currentUser } = useAuthStore()
  const { cartItems, getTotalQuantity } = useCartStore()
  const { getDefaultAddress } = useAddressStore()
  const {
    initializeWithUserData,
    initializeWithSavedAddress,
    validateCheckout,
    getCheckoutPayload,
    resetCheckout,
    isSubmitting,
    setErrors
  } = useCheckoutStore()

  const [showLoginPrompt, setShowLoginPrompt] = useState(false)

  // Check authentication and cart on mount
  useEffect(() => {
    if (!isAuthenticated) {
      setShowLoginPrompt(true)
      return
    }

    if (cartItems.length === 0) {
      toast.error('Your cart is empty')
      navigate('/cart')
      return
    }

    // Initialize checkout with user data
    if (currentUser) {
      initializeWithUserData(currentUser)
    }

    // Initialize with saved address if available
    const defaultAddress = getDefaultAddress()
    if (defaultAddress) {
      initializeWithSavedAddress(defaultAddress)
    }
  }, [isAuthenticated, cartItems.length, currentUser, initializeWithUserData, navigate])

  // Handle login prompt close
  const handleLoginPromptClose = () => {
    setShowLoginPrompt(false)
    navigate('/cart') // Redirect to cart if user closes without logging in
  }

  // Handle place order
  const handlePlaceOrder = async () => {
    try {
      // Clear previous errors
      setErrors({})

      // Validate checkout form
      const isValid = validateCheckout()
      if (!isValid) {
        toast.error('Please fill in all required fields correctly')
        return
      }

      // Get checkout payload
      const checkoutData = getCheckoutPayload()

      // Simulate order processing
      toast.loading('Processing your order...', { id: 'checkout' })

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Success
      toast.success('Order placed successfully!', { id: 'checkout' })

      // Log checkout data (replace with actual API call)
      console.log('Checkout Data:', {
        ...checkoutData,
        items: cartItems,
        orderTotal: getTotalQuantity(),
        timestamp: new Date().toISOString()
      })

      // Reset checkout and redirect to success page
      resetCheckout()
      navigate('/order-success', {
        state: {
          orderData: checkoutData,
          items: cartItems
        }
      })

    } catch (error) {
      console.error('Checkout error:', error)
      toast.error('Failed to place order. Please try again.', { id: 'checkout' })
    }
  }

  // Don't render if not authenticated
  if (!isAuthenticated) {
    return (
      <LoginPromptModal
        isOpen={showLoginPrompt}
        onClose={handleLoginPromptClose}
        action="proceed to checkout"
      />
    )
  }

  // Don't render if cart is empty
  if (cartItems.length === 0) {
    return null
  }

  return (
    <div className="min-h-screen">
      <SEO
        title="Checkout"
        description={`Complete your order of ${getTotalQuantity()} religious and spiritual products. Secure checkout with multiple payment options at ShringarHub.`}
        keywords="checkout, secure payment, order completion, religious products checkout, spiritual items purchase, shringarhub checkout"
      />

      {/* Breadcrumb */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <nav className="flex items-center space-x-2 text-sm">
            <Link to="/" className="text-gray-500 hover:text-primary">Home</Link>
            <span className="text-gray-400">/</span>
            <Link to="/cart" className="text-gray-500 hover:text-primary">Cart</Link>
            <span className="text-gray-400">/</span>
            <span className="text-gray-900 font-medium">Checkout</span>
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Two Column Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Header + Forms */}
          <div className="lg:col-span-2 space-y-6">
            {/* Header inside left column */}
            <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
              <div className="flex items-center space-x-4 mb-4">
                <button
                  onClick={() => navigate('/cart')}
                  className="flex items-center space-x-2 text-gray-600 hover:text-primary transition-colors"
                >
                  <FaArrowLeft className="w-4 h-4" />
                  <span>Back to Cart</span>
                </button>
              </div>
              <h1 className="text-3xl font-bold text-gray-900 mb-2">Checkout</h1>
              <p className="text-gray-600">
                Complete your order for {getTotalQuantity()} {getTotalQuantity() === 1 ? 'item' : 'items'}
              </p>
            </div>
            {/* Address Selector */}
            <AddressSelector />

            {/* Billing Address Selector */}
            <BillingAddressSelector />

            {/* Payment Options */}
            <PaymentOptions />
          </div>

          {/* Right Column - Order Summary */}
          <div className="lg:col-span-1">
            <div className="sticky top-6">
              <OrderSummary
                onPlaceOrder={handlePlaceOrder}
                isSubmitting={isSubmitting}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Checkout
