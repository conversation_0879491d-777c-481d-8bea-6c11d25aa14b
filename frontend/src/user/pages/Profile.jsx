import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { FaSignOutAlt, FaUser, FaCreditCard, FaMapMarkerAlt, Fa<PERSON>ey, FaClipboardList, FaChevronRight } from 'react-icons/fa'
import { toast } from 'sonner'
import useAuthStore from '../../store/authStore'
import useUserProfileStore from '../../store/userProfileStore'
import SEO from '../../shared/ui/SEO'
import OrderHistory from '../components/profile/OrderHistory'
import PersonalInfo from '../components/profile/PersonalInfo'
import SavedCards from '../components/profile/SavedCards'
import ShippingAddresses from '../components/profile/ShippingAddresses'
import ChangePassword from '../components/profile/ChangePassword'

const Profile = () => {
  const navigate = useNavigate()
  const { logout } = useAuthStore()
  const { clearUserData, userInfo, orders } = useUserProfileStore()
  const [activeSection, setActiveSection] = useState('orders')
  const [showMobileContent, setShowMobileContent] = useState(false)

  const handleLogout = () => {
    clearUserData()
    logout()
    toast.success('Logged out successfully')
    navigate('/')
  }

  const menuItems = [
    { id: 'orders', label: 'Order History', icon: FaClipboardList },
    { id: 'personal', label: 'Personal Information', icon: FaUser },
    { id: 'cards', label: 'Saved Cards', icon: FaCreditCard },
    { id: 'addresses', label: 'Shipping Addresses', icon: FaMapMarkerAlt },
    { id: 'password', label: 'Change Password', icon: FaKey },
  ]

  const handleMobileMenuClick = (sectionId) => {
    setActiveSection(sectionId)
    setShowMobileContent(true)
  }

  const handleMobileBack = () => {
    setShowMobileContent(false)
  }

  const getCurrentSectionTitle = () => {
    const currentItem = menuItems.find(item => item.id === activeSection)
    return currentItem ? currentItem.label : 'Order History'
  }

  const renderContent = () => {
    switch (activeSection) {
      case 'personal':
        return <PersonalInfo />
      case 'orders':
        return <OrderHistory />
      case 'cards':
        return <SavedCards />
      case 'addresses':
        return <ShippingAddresses />
      case 'password':
        return <ChangePassword />
      default:
        return <OrderHistory />
    }
  }

  // Get dynamic SEO data based on active section
  const getSEOData = () => {
    switch (activeSection) {
      case 'orders':
        return {
          title: 'My Orders',
          description: 'View and manage your order history at ShringarHub. Track your religious and spiritual product orders.',
          keywords: 'my orders, order history, track orders, shringarhub orders, religious products orders'
        }
      case 'personal':
        return {
          title: 'Personal Information',
          description: 'Manage your personal information and account details at ShringarHub.',
          keywords: 'personal information, account details, profile settings, user profile'
        }
      case 'addresses':
        return {
          title: 'Shipping Addresses',
          description: 'Manage your shipping addresses for faster checkout at ShringarHub.',
          keywords: 'shipping addresses, delivery addresses, address book, manage addresses'
        }
      case 'cards':
        return {
          title: 'Saved Cards',
          description: 'Manage your saved payment methods for secure and quick checkout.',
          keywords: 'saved cards, payment methods, secure checkout, payment cards'
        }
      case 'password':
        return {
          title: 'Change Password',
          description: 'Update your account password for enhanced security.',
          keywords: 'change password, account security, password update, secure account'
        }
      default:
        return {
          title: 'My Account',
          description: 'Manage your ShringarHub account, orders, and personal information.',
          keywords: 'my account, user profile, account management, shringarhub account'
        }
    }
  }

  const seoData = getSEOData()

  return (
    <div className="min-h-screen bg-white">
      <SEO
        key={activeSection} // Force re-render when section changes
        title={seoData.title}
        description={seoData.description}
        keywords={seoData.keywords}
      />

      <div className="max-w-[1600px] mx-auto px-4 sm:px-8 lg:px-12 py-8 sm:py-16">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 sm:space-x-3 text-sm sm:text-lg text-gray-600 mb-8 sm:mb-16">
          <button onClick={() => navigate('/')} className="hover:text-black transition-colors">
            Home
          </button>
          <span>/</span>
          <span className="text-black font-medium">My Account</span>
        </nav>

        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-12 sm:mb-20 pb-8 sm:pb-12 border-b border-gray-200">
          <div className="mb-4 sm:mb-0">
            <h1 className="text-2xl sm:text-4xl xl:text-5xl font-medium text-black mb-2 sm:mb-4">My Account</h1>
            <p className="text-gray-600 text-base sm:text-xl xl:text-2xl">Hello, {userInfo?.name?.split(' ')[0]}</p>
          </div>
          <button
            onClick={handleLogout}
            className="text-sm sm:text-lg text-gray-600 hover:text-black underline transition-colors self-start sm:self-auto"
          >
            Sign Out
          </button>
        </div>

        {/* Mobile & Tablet View */}
        <div className="xl:hidden">
          {!showMobileContent ? (
            // Mobile Menu
            <div className="space-y-1 sm:space-y-2">
              <h2 className="text-base sm:text-xl font-medium text-black mb-6 sm:mb-12 tracking-wide">MANAGE ACCOUNT</h2>
              {menuItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleMobileMenuClick(item.id)}
                  className="w-full flex items-center justify-between px-0 sm:px-0 py-4 sm:py-6 text-left text-base sm:text-lg border-b border-gray-200 hover:bg-gray-50 transition-colors text-gray-700 hover:text-black"
                >
                  <span>{item.label}</span>
                  <FaChevronRight className="w-4 h-4 sm:w-5 sm:h-5" />
                </button>
              ))}
            </div>
          ) : (
            // Mobile Content
            <div>
              <div className="flex items-center mb-8 sm:mb-12">
                <button
                  onClick={handleMobileBack}
                  className="flex items-center text-gray-600 hover:text-black mr-4 sm:mr-6"
                >
                  <FaChevronRight className="w-4 h-4 sm:w-5 sm:h-5 transform rotate-180 mr-2 sm:mr-3" />
                  <span className="text-base sm:text-lg">Back</span>
                </button>
                <h2 className="text-xl sm:text-2xl font-normal text-black">{getCurrentSectionTitle()}</h2>
              </div>
              <div className="max-w-full sm:max-w-4xl">
                {renderContent()}
              </div>
            </div>
          )}
        </div>

        {/* Desktop View */}
        <div className="hidden lg:grid lg:grid-cols-12 gap-20">

          {/* Sidebar Navigation */}
          <div className="lg:col-span-4 border-r border-gray-200 pr-16">
            <div className="space-y-2">
              <h2 className="text-xl font-medium text-black mb-12 tracking-wide">MANAGE ACCOUNT</h2>
              {menuItems.map((item) => (
                <button
                  key={item.id}
                  onClick={() => setActiveSection(item.id)}
                  className={`w-full flex items-center justify-between px-0 py-6 text-left text-lg border-b border-gray-100 hover:bg-gray-50 transition-colors ${
                    activeSection === item.id ? 'text-black font-semibold bg-gray-50' : 'text-gray-700 hover:text-black'
                  }`}
                >
                  <span>{item.label}</span>
                  <FaChevronRight className="w-5 h-5" />
                </button>
              ))}
            </div>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-8">
            <div className="max-w-4xl">
              {renderContent()}
            </div>
          </div>

        </div>
      </div>
    </div>
  )
}

export default Profile
