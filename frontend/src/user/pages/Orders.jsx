import React, { useState } from 'react'
import { FaClipboardList } from 'react-icons/fa'
import SEO from '../../shared/ui/SEO'
import OrderCard from '../components/orders/OrderCard'
import dummyProducts from '../../data/dummyProducts'

const Orders = () => {
  // Generate orders using existing product data
  const [orders] = useState([
    {
      id: 'ORD-2024-001',
      date: '2024-01-15',
      items: [
        {
          id: dummyProducts[0].id,
          title: dummyProducts[0].name,
          thumbnail: dummyProducts[0].image,
          quantity: 1,
          price: dummyProducts[0].price
        },
        {
          id: dummyProducts[1].id,
          title: dummyProducts[1].name,
          thumbnail: dummyProducts[1].image,
          quantity: 2,
          price: dummyProducts[1].price
        }
      ],
      totalAmount: dummyProducts[0].price + (dummyProducts[1].price * 2)
    },
    {
      id: 'ORD-2024-002',
      date: '2024-01-20',
      items: [
        {
          id: dummyProducts[2].id,
          title: dummyProducts[2].name,
          thumbnail: dummyProducts[2].image,
          quantity: 1,
          price: dummyProducts[2].price
        },
        {
          id: dummyProducts[3].id,
          title: dummyProducts[3].name,
          thumbnail: dummyProducts[3].image,
          quantity: 3,
          price: dummyProducts[3].price
        }
      ],
      totalAmount: dummyProducts[2].price + (dummyProducts[3].price * 3)
    },
    {
      id: 'ORD-2024-003',
      date: '2024-01-25',
      items: [
        {
          id: dummyProducts[4].id,
          title: dummyProducts[4].name,
          thumbnail: dummyProducts[4].image,
          quantity: 1,
          price: dummyProducts[4].price
        },
        {
          id: dummyProducts[5].id,
          title: dummyProducts[5].name,
          thumbnail: dummyProducts[5].image,
          quantity: 1,
          price: dummyProducts[5].price
        }
      ],
      totalAmount: dummyProducts[4].price + dummyProducts[5].price
    },
    {
      id: 'ORD-2024-004',
      date: '2024-02-01',
      items: [
        {
          id: dummyProducts[6].id,
          title: dummyProducts[6].name,
          thumbnail: dummyProducts[6].image,
          quantity: 1,
          price: dummyProducts[6].price
        },
        {
          id: dummyProducts[7].id,
          title: dummyProducts[7].name,
          thumbnail: dummyProducts[7].image,
          quantity: 2,
          price: dummyProducts[7].price
        },
        {
          id: dummyProducts[8].id,
          title: dummyProducts[8].name,
          thumbnail: dummyProducts[8].image,
          quantity: 1,
          price: dummyProducts[8].price
        }
      ],
      totalAmount: dummyProducts[6].price + (dummyProducts[7].price * 2) + dummyProducts[8].price
    }
  ])

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getTotalItems = (items) => {
    return items.reduce((total, item) => total + item.quantity, 0)
  }

  return (
    <div>
      <SEO
        title="My Orders"
        description="View and manage your order history at ShringarHub. Track your religious and spiritual product orders and delivery status."
        keywords="my orders, order history, track orders, shringarhub orders, religious products orders, order tracking"
      />

      {/* Page Header */}
      <div className="flex items-center space-x-3 mb-8">
        <FaClipboardList className="w-6 h-6 text-primary" />
        <h2 className="text-2xl font-semibold text-black">Order History</h2>
      </div>

      {/* Orders List */}
      {orders.length === 0 ? (
        <div className="text-center py-16">
          <FaClipboardList className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No orders yet</h3>
          <p className="text-gray-600 mb-6">When you place your first order, it will appear here.</p>
          <button
            onClick={() => window.location.href = '/products'}
            className="bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary-dark transition-colors"
          >
            Start Shopping
          </button>
        </div>
      ) : (
        <div className="space-y-6">
          {orders.map((order) => (
            <OrderCard
              key={order.id}
              order={order}
              formatCurrency={formatCurrency}
              formatDate={formatDate}
              getTotalItems={getTotalItems}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export default Orders
