import React, { useState } from 'react'
import { FaMapMarkerAlt, FaPlus } from 'react-icons/fa'
import SEO from '../../shared/ui/SEO'
import AddressCard from '../components/account/AddressCard'
import AddressFormModal from '../components/account/AddressFormModal'
import useAddressStore from '../../store/addressStore'

const ShippingAddress = () => {
  const { addresses, addAddress, updateAddress, deleteAddress, setDefaultAddress } = useAddressStore()
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingAddress, setEditingAddress] = useState(null)

  const handleAddAddress = (addressData) => {
    addAddress(addressData)
    setIsModalOpen(false)
  }

  const handleEditAddress = (addressData) => {
    updateAddress(editingAddress.id, addressData)
    setEditingAddress(null)
    setIsModalOpen(false)
  }

  const handleDeleteAddress = (addressId) => {
    if (window.confirm('Are you sure you want to delete this address?')) {
      deleteAddress(addressId)
    }
  }

  const handleSetDefault = (addressId) => {
    setDefaultAddress(addressId)
  }

  const openAddModal = () => {
    setEditingAddress(null)
    setIsModalOpen(true)
  }

  const openEditModal = (address) => {
    setEditingAddress(address)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    setEditingAddress(null)
  }

  return (
    <div>
      <SEO
        title="Shipping Addresses"
        description="Manage your shipping addresses for faster checkout at ShringarHub. Add, edit, and organize your delivery addresses for religious and spiritual products."
        keywords="shipping addresses, delivery addresses, address book, manage addresses, checkout addresses, shipping information"
      />

      {/* Page Header */}
      <div className="flex items-center space-x-3 mb-8">
        <FaMapMarkerAlt className="w-6 h-6 text-primary" />
        <h2 className="text-2xl font-semibold text-black">Shipping Addresses</h2>
      </div>

      {/* Content */}
      {addresses.length === 0 ? (
        /* Empty State */
        <div className="text-center py-16">
          <FaMapMarkerAlt className="w-16 h-16 text-gray-300 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            You currently don't have any addresses saved
          </h3>
          <p className="text-gray-600 mb-6">
            Add an address for a faster checkout experience!
          </p>
          <button
            onClick={openAddModal}
            className="text-primary hover:text-primary-dark underline font-medium"
          >
            Add an address
          </button>
        </div>
      ) : (
        /* Address List */
        <div className="space-y-6">
          {/* Existing Addresses */}
          <div className="grid gap-4">
            {addresses.map((address) => (
              <AddressCard
                key={address.id}
                address={address}
                onEdit={() => openEditModal(address)}
                onDelete={() => handleDeleteAddress(address.id)}
                onSetDefault={() => handleSetDefault(address.id)}
              />
            ))}
          </div>

          {/* Add New Address Button */}
          <div className="pt-4 border-t border-gray-200">
            <button
              onClick={openAddModal}
              className="flex items-center space-x-2 text-primary hover:text-primary-dark font-medium transition-colors"
            >
              <FaPlus className="w-4 h-4" />
              <span>Add New Address</span>
            </button>
          </div>
        </div>
      )}

      {/* Address Form Modal */}
      <AddressFormModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onSave={editingAddress ? handleEditAddress : handleAddAddress}
        initialData={editingAddress}
        title={editingAddress ? 'Edit Address' : 'Add New Address'}
      />
    </div>
  )
}

export default ShippingAddress
