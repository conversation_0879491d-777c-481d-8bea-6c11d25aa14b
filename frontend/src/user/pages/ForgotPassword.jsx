import React, { useState } from "react";
import Input from "../../shared/ui/Input";
import Button from "../../shared/ui/Button";
import logo from "../../assets/Logo1.png";
import { Link } from "react-router-dom";
import LinkText from "../../shared/ui/LinkText";
import { AiOutlineLoading3Quarters } from "react-icons/ai";
import { FaEnvelope, FaArrowLeft } from "react-icons/fa";
import useAuthStore from "../../store/authStore";
import { toast } from 'sonner'
import SEO from "../../shared/ui/SEO";

const ForgotPassword = () => {
  const { resetPassword, isLoading, error, clearError } = useAuthStore();

  const [formData, setFormData] = useState({
    email: ""
  });

  const [errors, setErrors] = useState({});
  const [emailSent, setEmailSent] = useState(false);

  // handle form data 
  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));

    // Clear error for that specific field if any
    setErrors((prevErrors) => ({
      ...prevErrors,
      [name]: '',
    }));
  };

  function validateForm(formData) {
    const errors = {};
  
    // Email validation
    if (!formData.email.trim()) {
      errors.email = "Email is required";
    } else if (!/^\S+@\S+\.\S+$/.test(formData.email)) {
      errors.email = "Enter a valid email address";
    }

    return errors;
  }

  // handle form submit
  const handleSubmit = async(e) => {
    e.preventDefault();

    // Clear any previous errors
    clearError();

    const validationErrors = validateForm(formData);

    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors);
      return;
    }

    setErrors({}); // Clear errors

    try {
      await resetPassword(formData.email);
      setEmailSent(true);
      toast.success("Password reset instructions sent to your email!");
    } catch (error) {
      toast.error(error.message || "Failed to send reset email. Please try again.");
    }
  };

  if (emailSent) {
    return (
      <div className="min-h-screen  flex items-center justify-center px-4 py-8">
        <SEO
          title="Reset Email Sent"
          description="Password reset email has been sent to your email address. Check your inbox and follow the instructions to reset your ShringarHub account password."
          keywords="password reset, email sent, forgot password, reset link, shringarhub password reset"
        />

        <div className="w-full max-w-md">
          <div className="bg-white rounded-3xl shadow-xl border border-gray-100 p-8 text-center">
            <div className="mb-8">
              <div className="w-20 h-20 bg-green-50 border-2 border-green-200 rounded-full flex items-center justify-center mx-auto mb-6">
                <FaEnvelope className="w-10 h-10 text-green-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-3">Check Your Email</h2>
              <p className="text-gray-600 leading-relaxed">
                We've sent password reset instructions to{" "}
                <span className="font-semibold text-primary break-all">{formData.email}</span>
              </p>
            </div>

            <div className="space-y-6">
              <p className="text-sm text-gray-500 leading-relaxed">
                Didn't receive the email? Check your spam folder or try again.
              </p>

              <div className="flex flex-col gap-3">
                <Button
                  onClick={() => {
                    setEmailSent(false);
                    setFormData({ email: "" });
                  }}
                  variant="outline"
                  className="w-full py-3 rounded-xl border-2 hover:bg-gray-50 hover:border-gray-300 transition-all duration-200 text-gray-700 hover:text-gray-900"
                >
                  Try Different Email
                </Button>

                <Link to="/login" className="w-full">
                  <Button variant="primary" className="w-full py-3 rounded-xl flex items-center justify-center">
                    <FaArrowLeft className="mr-2 w-4 h-4" />
                    <span>Back to Login</span>
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen  flex items-center justify-center px-4 py-8">
      <SEO
        title="Forgot Password"
        description="Reset your ShringarHub account password. Enter your email address and we'll send you a link to reset your password securely."
        keywords="forgot password, password reset, reset password, account recovery, shringarhub password"
      />

      <div className="w-full max-w-md">
        <div className="bg-white rounded-3xl shadow-xl border border-gray-100 overflow-hidden">

          {/* Header */}
          <div className="bg-white p-8 text-center">
            <Link to="/" className="inline-block mb-6">
              <img
                src={logo}
                alt="ShringarHub"
                className="w-20 h-20 object-contain rounded-full mx-auto hover:scale-105 transition-transform duration-200"
              />
            </Link>
            <h1 className="text-2xl md:text-3xl font-bold sm text-gray-900 mb-3">Forgot Password?</h1>
            <p className="text-gray-600 leading-relaxed">
              No worries! Enter your email and we'll send you reset instructions.
            </p>
          </div>

          {/* Form */}
          <div className="px-8 pb-8">
            <form onSubmit={handleSubmit} className="space-y-6" noValidate>

              {/* Email Input */}
              <div>
                <Input
                  label="Email Address"
                  placeholder="Enter your email address"
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  error={errors.email}
                  className="w-full"
                  autoComplete="email"
                />
              </div>

              {/* Submit Button */}
              <Button
                type="submit"
                disabled={isLoading}
                variant="primary"
                className="w-full py-4 rounded-xl text-lg font-semibold transition-all duration-300 hover:shadow-lg"
              >
                {isLoading && (
                  <AiOutlineLoading3Quarters className="animate-spin mr-2 inline-block" />
                )}
                {isLoading ? "Sending..." : "Send Reset Instructions"}
              </Button>

              {/* Back to Login */}
              <div className="text-center  pt-2">
                <Link
                  to="/login"
                  className="inline-flex items-center justify-center text-gray-600 hover:text-primary transition-colors duration-200 font-medium"
                >
                  <FaArrowLeft className="mr-2 text-sm " />
                  Back to Login
                </Link>
              </div>

              {/* Sign Up Link */}
              <div className="text-center pt-6 border-t border-gray-100">
                <p className="text-sm text-gray-600">
                  Don't have an account?{" "}
                  <LinkText to="/register">Create Account</LinkText>
                </p>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ForgotPassword;
