import React, { useState } from 'react'
import { FaPaperPlane } from 'react-icons/fa'
import Button from '../../shared/ui/Button'
import Input from '../../shared/ui/Input'
import Textarea from '../../shared/ui/Textarea'
import SEO from '../../shared/ui/SEO'
import { toast } from 'sonner'

const Contact = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Simulate form submission
    setTimeout(() => {
      console.log('Form submitted:', formData)

      // Show success toast notification
      toast.success('Message sent successfully!', {
        description: 'Thank you for reaching out. We will get back to you soon.',
        duration: 4000,
      })

      setFormData({ name: '', email: '', message: '' })
      setIsSubmitting(false)
    }, 1000)
  }

  return (
    <div className="min-h-screen bg-background">
      <SEO
        title="Contact Us"
        description="Get in touch with ShringarHub. We'd love to hear from you! Whether you have questions about our religious products or need spiritual guidance, we're here to help."
        keywords="contact shringarhub, religious products support, spiritual guidance, customer service, religious items help"
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Main Content */}
        <div className="max-w-6xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-text mb-6 font-serif">
              Contact Us
            </h1>
            <div className="w-24 h-1 bg-primary mx-auto mb-8"></div>
            <p className="text-lg text-gray-700 max-w-2xl mx-auto leading-relaxed">
              We'd love to hear from you! Whether you have questions about our products, or want to share your experience, we're here to help.
            </p>
          </div>

          {/* Contact Form */}
          <div className="max-w-2xl mx-auto">
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <h2 className="text-2xl font-bold text-text mb-6 font-serif">
                Send us a Message
              </h2>
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name Field */}
                <Input
                  label="Full Name"
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="Enter your full name"
                  required
                  className="py-3 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                />

                {/* Email Field */}
                <Input
                  label="Email Address"
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="Enter your email address"
                  required
                  className="py-3 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                />

                {/* Message Field */}
                <Textarea
                  label="Message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  placeholder="Tell us how we can help you..."
                  required
                  rows={5}
                  className="py-3 rounded-lg focus:ring-2 focus:ring-primary focus:border-primary transition-colors"
                />

                {/* Submit Button */}
                <Button
                  type="submit"
                  variant="primary"
                  size="lg"
                  disabled={isSubmitting}
                  className="w-full rounded-lg transition-all duration-300 hover:shadow-lg flex items-center justify-center gap-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      Sending...
                    </>
                  ) : (
                    <>
                      <FaPaperPlane className="w-4 h-4" />
                      Send Message
                    </>
                  )}
                </Button>
              </form>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Contact
