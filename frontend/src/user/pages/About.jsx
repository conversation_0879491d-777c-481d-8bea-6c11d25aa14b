import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { FaHome, FaChevronRight, FaHeart, FaStar, FaHandsHelping, FaLeaf } from 'react-icons/fa'
import SEO from '../../shared/ui/SEO'

const About = () => {
  return (
    <div className="min-h-screen bg-background">
      <SEO
        title="About Us"
        description="Learn about ShringarHub - your trusted source for authentic religious and spiritual products. We are passionate about bringing you the finest collection of sacred items that enhance your spiritual journey."
        keywords="about shringarhub, religious products company, spiritual items store, authentic religious products, hindu religious items, spiritual journey"
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumbs */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <Link 
            to="/" 
            className="hover:text-primary transition-colors flex items-center"
          >
            <FaHome className="w-4 h-4" />
          </Link>
          <FaChevronRight className="w-3 h-3 text-gray-400" />
          <span className="text-primary font-medium">About Us</span>
        </nav>

        {/* Main Content */}
        <div className="max-w-4xl mx-auto">
          {/* Header Section */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-bold text-text mb-6 font-serif">
              About Us
            </h1>
            <div className="w-24 h-1 bg-primary mx-auto mb-8"></div>
          </div>

          {/* Who We Are Section */}
          <div className="bg-white rounded-2xl shadow-lg p-8 md:p-12 mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-text mb-6 font-serif text-center">
              Who We Are
            </h2>
            <p className="text-lg text-gray-700 leading-relaxed text-center max-w-3xl mx-auto">
              Welcome to <span className="font-semibold text-primary">ShringarHub</span>, your trusted destination for authentic religious and spiritual products. We are passionate about bringing you the finest collection of sacred items that enhance your spiritual journey and connect you with the divine.
            </p>
            <p className="text-lg text-gray-700 leading-relaxed text-center max-w-3xl mx-auto mt-6">
              From beautifully crafted statues and traditional poshaks to elegant brass items and spiritual accessories, we curate each product with love, devotion, and respect for our rich cultural heritage.
            </p>
          </div>

          {/* Mission and Values Section */}
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {/* Mission */}
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-primary rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaStar className="w-8 h-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold text-text font-serif">Our Mission</h3>
              </div>
              <p className="text-gray-700 leading-relaxed text-center">
                To provide authentic, high-quality religious and spiritual products that inspire devotion, enhance worship experiences, and preserve our sacred traditions for future generations.
              </p>
            </div>

            {/* Values */}
            <div className="bg-white rounded-2xl shadow-lg p-8">
              <div className="text-center mb-6">
                <div className="w-16 h-16 bg-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaHeart className="w-8 h-8 text-text" />
                </div>
                <h3 className="text-2xl font-bold text-text font-serif">Our Values</h3>
              </div>
              <p className="text-gray-700 leading-relaxed text-center">
                We believe in authenticity, quality, and devotion. Every product we offer is carefully selected to meet the highest standards of craftsmanship and spiritual significance.
              </p>
            </div>
          </div>

          {/* What Makes Us Special */}
          <div className="bg-white rounded-2xl shadow-lg p-8 md:p-12 mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-text mb-8 font-serif text-center">
              What Makes Us Special
            </h2>
            <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-8">
              {/* Quality */}
              <div className="text-center">
                <div className="w-12 h-12 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaStar className="w-6 h-6 text-primary" />
                </div>
                <h4 className="text-lg font-semibold text-text mb-2">Premium Quality</h4>
                <p className="text-gray-600 text-sm">
                  Handpicked products crafted with attention to detail and traditional techniques.
                </p>
              </div>

              {/* Authenticity */}
              <div className="text-center">
                <div className="w-12 h-12 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaLeaf className="w-6 h-6 text-primary" />
                </div>
                <h4 className="text-lg font-semibold text-text mb-2">Authentic Products</h4>
                <p className="text-gray-600 text-sm">
                  Genuine religious items sourced from trusted artisans and manufacturers.
                </p>
              </div>

              {/* Service */}
              <div className="text-center sm:col-span-2 lg:col-span-1">
                <div className="w-12 h-12 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
                  <FaHandsHelping className="w-6 h-6 text-primary" />
                </div>
                <h4 className="text-lg font-semibold text-text mb-2">Dedicated Service</h4>
                <p className="text-gray-600 text-sm">
                  Committed to providing exceptional customer service and spiritual guidance.
                </p>
              </div>
            </div>
          </div>

          {/* Our Promise */}
          <div className="bg-gradient-to-r from-primary to-red-700 rounded-2xl shadow-lg p-8 md:p-12 text-center text-white">
            <h2 className="text-2xl md:text-3xl font-bold mb-6 font-serif">
              Our Promise to You
            </h2>
            <p className="text-lg leading-relaxed max-w-2xl mx-auto mb-8">
              At ShringarHub, we promise to deliver not just products, but experiences that enrich your spiritual journey. Every item in our collection is chosen with care, blessed with good intentions, and delivered with love.
            </p>
            <Link
              to="/products"
              className="inline-flex items-center px-8 py-3 bg-white text-primary font-semibold rounded-full hover:bg-gray-100 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              Explore Our Collection
              <FaChevronRight className="ml-2 w-4 h-4" />
            </Link>
          </div>

          {/* Contact Section */}
          <div className="text-center mt-12 bg-white rounded-2xl shadow-lg p-8">
            <h3 className="text-xl font-bold text-text mb-4 font-serif">
              Have Questions?
            </h3>
            <p className="text-gray-700 mb-6">
              We're here to help you find the perfect spiritual products for your needs.
            </p>
            <Link
              to="/contact"
              className="inline-flex items-center px-6 py-3 bg-primary text-white font-semibold rounded-lg hover:bg-red-700 transition-colors"
            >
              Contact Us
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}

export default About
