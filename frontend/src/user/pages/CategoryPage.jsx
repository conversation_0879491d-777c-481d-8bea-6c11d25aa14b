import React, { useState, useMemo } from 'react'
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom'
import { FaHome, FaChevronRight, FaFilter, FaSortAmountDown } from 'react-icons/fa'
import { getProductsByCategory, getCategoryDisplayName } from '../../utils/categoryUtils'
import ProductCard from '../components/ProductCard'
import SEO from '../../shared/ui/SEO'

const CategoryPage = () => {
  const { categoryName } = useParams()
  const [sortBy, setSortBy] = useState('name')
  const [sortOrder, setSortOrder] = useState('asc')

  // Get products for this category
  const categoryProducts = useMemo(() => {
    return getProductsByCategory(categoryName)
  }, [categoryName])

  // Sort products based on selected criteria
  const sortedProducts = useMemo(() => {
    const sorted = [...categoryProducts].sort((a, b) => {
      let aValue, bValue

      switch (sortBy) {
        case 'price':
          aValue = a.price
          bValue = b.price
          break
        case 'name':
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
        default:
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
      }

      if (sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })

    return sorted
  }, [categoryProducts, sortBy, sortOrder])

  const displayCategoryName = getCategoryDisplayName(categoryName)

  const handleSortChange = (newSortBy) => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(newSortBy)
      setSortOrder('asc')
    }
  }

  // Get display name for the category
  const displayName = getCategoryDisplayName(categoryName)

  return (
    <div className="min-h-screen ">
      <SEO
        key={categoryName} // Force re-render when category changes
        title={`${displayName}`}
        description={`Browse our collection of ${displayName.toLowerCase()} - Authentic religious and spiritual products at ShringarHub. Find the perfect ${displayName.toLowerCase()} for your spiritual journey.`}
        keywords={`${displayName.toLowerCase()}, religious ${displayName.toLowerCase()}, spiritual ${displayName.toLowerCase()}, hindu ${displayName.toLowerCase()}, authentic ${displayName.toLowerCase()}, religious products`}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <Link to="/" className="hover:text-primary transition-colors">
            <FaHome className="w-4 h-4" />
          </Link>
          <FaChevronRight className="w-3 h-3" />
          <Link to="/products" className="hover:text-primary transition-colors">
            Products
          </Link>
          <FaChevronRight className="w-3 h-3" />
          <span className="text-primary font-medium">{displayCategoryName}</span>
        </nav>

        {/* Page Header */}
        <div className="mb-8">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-2">
            {displayCategoryName}
          </h1>
          <p className="text-gray-600">
            {categoryProducts.length} {categoryProducts.length === 1 ? 'product' : 'products'} found
          </p>
        </div>

        {/* Filters and Sort */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4 mb-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div className="flex items-center gap-2">
              <FaFilter className="w-4 h-4 text-gray-500" />
              <span className="text-sm font-medium text-gray-700">Filter & Sort</span>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              {/* Sort Options */}
              <div className="flex items-center gap-2">
                <FaSortAmountDown className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600">Sort by:</span>
                <div className="flex gap-2">
                  <button
                    onClick={() => handleSortChange('name')}
                    className={`px-3 py-1 text-sm rounded-full transition-colors ${
                      sortBy === 'name'
                        ? 'bg-primary text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Name {sortBy === 'name' && (sortOrder === 'asc' ? '↑' : '↓')}
                  </button>
                  <button
                    onClick={() => handleSortChange('price')}
                    className={`px-3 py-1 text-sm rounded-full transition-colors ${
                      sortBy === 'price'
                        ? 'bg-primary text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    Price {sortBy === 'price' && (sortOrder === 'asc' ? '↑' : '↓')}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Products Grid */}
        {sortedProducts.length > 0 ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {sortedProducts.map((product) => (
              <div key={product.id} className="flex">
                <ProductCard product={product} />
              </div>
            ))}
          </div>
        ) : (
          /* No Products Found */
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                <FaFilter className="w-8 h-8 text-gray-400" />
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">
                No items found in this category
              </h3>
              <p className="text-gray-600 mb-6">
                We couldn't find any products in the "{displayCategoryName}" category.
              </p>
              <div className="space-y-3">
                <Link
                  to="/products"
                  className="inline-block px-6 py-3 bg-primary text-white font-semibold rounded-lg hover:bg-red-700 transition-colors"
                >
                  View All Products
                </Link>
                <br />
                <Link
                  to="/"
                  className="inline-block px-6 py-3 bg-gray-100 text-gray-700 font-semibold rounded-lg hover:bg-gray-200 transition-colors"
                >
                  Back to Home
                </Link>
              </div>
            </div>
          </div>
        )}

        {/* Back to Categories */}
        {sortedProducts.length > 0 && (
          <div className="text-center mt-12">
            <Link
              to="/products"
              className="inline-flex items-center px-6 py-3 bg-primary text-white font-semibold rounded-lg hover:bg-gray-200 transition-colors"
            >
              View All Products
              <FaChevronRight className="ml-2 w-4 h-4" />
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}

export default CategoryPage
