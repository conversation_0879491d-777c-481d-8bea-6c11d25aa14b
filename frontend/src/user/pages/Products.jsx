import React, { useMemo, useEffect } from 'react'
import ProductCard from '../components/ProductCard'
import dummyProducts from '../../data/dummyProducts'
import { FaChevronLeft, FaChevronRight, FaFilter, FaTimes, FaHome, FaChevronRight as FaBreadcrumbArrow } from 'react-icons/fa'
import Button from '../../shared/ui/Button'
import useProductStore from '../../store/productStore'
import SEO from '../../shared/ui/SEO'
import { Link } from 'react-router-dom'

const Products = () => {
  const {
    searchQuery,
    selectedCategory,
    sortBy,
    priceRange,
    currentPage,
    productsPerPage,
    isLoading,
    isMobileFilterOpen,

    setSelectedCategory,
    setSortBy,
    setPriceRange,
    setCurrentPage,
    setIsLoading,
    toggleMobileFilter,
    closeMobileFilter,
    clearAllFilters,
    getFilteredProducts,
    getPaginatedProducts
  } = useProductStore()

  // Get unique categories
  const categories = ['All', ...new Set(dummyProducts.map(product => product.category))]

  // Filter and sort products using store logic
  const filteredAndSortedProducts = useMemo(() => {
    return getFilteredProducts(dummyProducts)
  }, [searchQuery, selectedCategory, sortBy, priceRange, getFilteredProducts])

  // Pagination calculations
  const totalProducts = filteredAndSortedProducts.length
  const totalPages = Math.ceil(totalProducts / productsPerPage)
  const startIndex = (currentPage - 1) * productsPerPage
  const endIndex = startIndex + productsPerPage
  const currentProducts = getPaginatedProducts(filteredAndSortedProducts)

  // Simulate loading when filters change
  useEffect(() => {
    setIsLoading(true)
    const timer = setTimeout(() => {
      setIsLoading(false)
    }, 300) // 300ms loading simulation

    return () => clearTimeout(timer)
  }, [searchQuery, selectedCategory, sortBy, priceRange, setIsLoading])

  // Handle filter changes with loading
  const handleCategoryChange = (category) => {
    setIsLoading(true)
    setSelectedCategory(category)
  }

  const handleSortChange = (sort) => {
    setIsLoading(true)
    setSortBy(sort)
  }

  const handlePriceRangeChange = (range) => {
    setIsLoading(true)
    setPriceRange(range)
  }

  // Dynamic SEO based on search and filters
  const getPageTitle = () => {
    if (searchQuery && searchQuery.trim()) return `Search: ${searchQuery}`
    if (selectedCategory && selectedCategory !== 'all') return `${selectedCategory} Products`
    return 'All Products'
  }

  const getPageDescription = () => {
    if (searchQuery && searchQuery.trim()) return `Search results for "${searchQuery}" - Find the perfect religious and spiritual products at ShringarHub.`
    if (selectedCategory && selectedCategory !== 'all') return `Browse our collection of ${selectedCategory.toLowerCase()} - Authentic religious and spiritual products at ShringarHub.`
    return 'Discover our complete collection of authentic religious and spiritual products including statues, poshaks, brass items, jewelry, and more sacred accessories.'
  }

  const getPageKeywords = () => {
    const baseKeywords = 'spiritual items, hindu products, religious accessories, sacred items, temple products'
    if (searchQuery && searchQuery.trim()) return `${searchQuery}, ${baseKeywords}`
    if (selectedCategory && selectedCategory !== 'all') return `${selectedCategory.toLowerCase()}, ${baseKeywords}`
    return `religious products, ${baseKeywords}`
  }

  // Debug logging to check if values are updating
  console.log('Products SEO Debug:', { searchQuery, selectedCategory, title: getPageTitle() })

  // Force component re-render when SEO-relevant data changes
  useEffect(() => {
    // This effect ensures the component re-renders when category or search changes
    // The SEO component will automatically update with new values
  }, [searchQuery, selectedCategory])

  return (
    <div className="min-h-screen">
      <SEO
        title={getPageTitle()}
        description={getPageDescription()}
        keywords={getPageKeywords()}
      />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumbs */}
        <nav className="flex items-center space-x-2 text-sm text-gray-600 mb-8">
          <Link
            to="/"
            className="hover:text-primary transition-colors flex items-center"
          >
            <FaHome className="w-4 h-4" />
          </Link>
          <FaBreadcrumbArrow className="w-3 h-3 text-gray-400" />
          <span className="text-primary font-medium">
            {searchQuery ? 'Search Results' : 'All Products'}
          </span>
          {searchQuery && (
            <>
              <FaBreadcrumbArrow className="w-3 h-3 text-gray-400" />
              <span className="text-gray-900 font-medium">
                "{searchQuery}"
              </span>
            </>
          )}
        </nav>

        {/* Header Section */}
        <div className="mb-8">
          <div className="text-center">
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-2">
              {searchQuery ? `Search Results` : 'Divine Products'}
            </h1>
            <p className="text-gray-600">
              {searchQuery
                ? `Found ${totalProducts} products for "${searchQuery}"`
                : `Discover our collection of ${dummyProducts.length} sacred items`
              }
            </p>
            {searchQuery && (
              <button
                onClick={() => {
                  clearAllFilters()
                  window.history.replaceState({}, '', '/products')
                }}
                className="mt-3 text-sm text-primary hover:text-primary-dark underline"
              >
                Clear search and view all products
              </button>
            )}
          </div>
        </div>

        {/* Mobile Filter Button */}
        <div className="lg:hidden mb-4">
          <Button
            onClick={toggleMobileFilter}
            variant="outline"
            className="w-full flex items-center justify-center gap-2"
          >
            <FaFilter className="w-4 h-4" />
            Filters & Sort
          </Button>
        </div>

        {/* Filters and Controls - Desktop */}
        <div className="hidden lg:block bg-white rounded-2xl shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">

            {/* Left Side - Filters */}
            <div className="flex flex-col sm:flex-row gap-4 flex-1">

              {/* Category Filter */}
              <div className="flex-1 min-w-0">
                <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                <select
                  value={selectedCategory}
                  onChange={(e) => handleCategoryChange(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                  disabled={isLoading}
                >
                  {categories.map(category => (
                    <option key={category} value={category}>{category}</option>
                  ))}
                </select>
              </div>

              {/* Sort Options */}
              <div className="flex-1 min-w-0">
                <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                <div className="flex gap-2">
                  <select
                    value={sortBy}
                    onChange={(e) => handleSortChange(e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                    disabled={isLoading}
                  >
                    <option value="name">Alphabetically (A-Z)</option>
                    <option value="name-desc">Alphabetically (Z-A)</option>
                    <option value="price">Price (Low to High)</option>
                    <option value="price-desc">Price (High to Low)</option>
                    <option value="date">Date Added (Newest)</option>
                    <option value="date-desc">Date Added (Oldest)</option>
                  </select>
                </div>
              </div>

              {/* Price Range */}
              <div className="flex-1 min-w-0">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Price Range: ₹{priceRange.min} - ₹{priceRange.max}
                </label>
                <div className="flex gap-2">
                  <input
                    type="range"
                    min="0"
                    max="2000"
                    value={priceRange.max}
                    onChange={(e) => handlePriceRangeChange({ ...priceRange, max: parseInt(e.target.value) })}
                    className="flex-1"
                    disabled={isLoading}
                  />
                </div>
              </div>
            </div>

            {/* Right Side - Clear Filters */}
            <div className="flex items-center">
              <Button
                onClick={clearAllFilters}
                variant="outline"
                size="sm"
                className="whitespace-nowrap"
                disabled={isLoading}
              >
                Clear Filters
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Filter Drawer */}
        {isMobileFilterOpen && (
          <div className="lg:hidden fixed inset-0 z-50 overflow-hidden">
            {/* Backdrop */}
            <div
              className="absolute inset-0 bg-black bg-opacity-50"
              onClick={closeMobileFilter}
            />

            {/* Drawer */}
            <div className="absolute right-0 top-0 h-full w-80 max-w-full bg-white shadow-xl">
              <div className="flex flex-col h-full">
                {/* Header */}
                <div className="flex items-center justify-between p-4 border-b">
                  <h3 className="text-lg font-semibold">Filters & Sort</h3>
                  <button
                    onClick={closeMobileFilter}
                    className="p-2 hover:bg-gray-100 rounded-full"
                  >
                    <FaTimes className="w-4 h-4" />
                  </button>
                </div>

                {/* Filter Content */}
                <div className="flex-1 overflow-y-auto p-4 space-y-6">
                  {/* Category Filter */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Category</label>
                    <select
                      value={selectedCategory}
                      onChange={(e) => handleCategoryChange(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                      disabled={isLoading}
                    >
                      {categories.map(category => (
                        <option key={category} value={category}>{category}</option>
                      ))}
                    </select>
                  </div>

                  {/* Sort Options */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Sort By</label>
                    <select
                      value={sortBy}
                      onChange={(e) => handleSortChange(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-primary focus:border-primary"
                      disabled={isLoading}
                    >
                      <option value="name">Alphabetically (A-Z)</option>
                      <option value="name-desc">Alphabetically (Z-A)</option>
                      <option value="price">Price (Low to High)</option>
                      <option value="price-desc">Price (High to Low)</option>
                      <option value="date">Date Added (Newest)</option>
                      <option value="date-desc">Date Added (Oldest)</option>
                    </select>
                  </div>

                  {/* Price Range */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Price Range: ₹{priceRange.min} - ₹{priceRange.max}
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="2000"
                      value={priceRange.max}
                      onChange={(e) => handlePriceRangeChange({ ...priceRange, max: parseInt(e.target.value) })}
                      className="w-full"
                      disabled={isLoading}
                    />
                  </div>
                </div>

                {/* Footer */}
                <div className="p-4 border-t space-y-3">
                  <Button
                    onClick={clearAllFilters}
                    variant="outline"
                    className="w-full"
                    disabled={isLoading}
                  >
                    Clear All Filters
                  </Button>
                  <Button
                    onClick={closeMobileFilter}
                    variant="primary"
                    className="w-full"
                  >
                    Apply Filters
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Results Summary */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6">
          <div className="mb-4 sm:mb-0">
            <p className="text-gray-600">
              Showing <span className="font-semibold">{startIndex + 1}-{Math.min(endIndex, totalProducts)}</span> of{' '}
              <span className="font-semibold">{totalProducts}</span> products
              {searchQuery && (
                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Search: "{searchQuery}"
                </span>
              )}
              {selectedCategory !== 'All' && (
                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary text-white">
                  {selectedCategory}
                </span>
              )}
            </p>
          </div>
        </div>

        {/* Products Grid */}
        {totalProducts === 0 ? (
          // Empty State
          <div className="text-center py-16">
            <div className="max-w-md mx-auto">
              <div className="w-24 h-24 mx-auto mb-6 bg-gray-100 rounded-full flex items-center justify-center">
                <div className="w-8 h-8 text-gray-400">📦</div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No products found</h3>
              <p className="text-gray-600 mb-6">
                Try adjusting your filter criteria to find what you're looking for.
              </p>
              <Button onClick={clearAllFilters} variant="primary">
                Clear All Filters
              </Button>
            </div>
          </div>
        ) : isLoading ? (
          // Loading Skeleton
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: productsPerPage }, (_, i) => (
              <div key={i} className="bg-white rounded-3xl shadow-lg border border-gray-100 p-4 animate-pulse">
                {/* Image Skeleton */}
                <div className="w-full h-48 sm:h-52 bg-gray-200 rounded-2xl mb-4"></div>

                {/* Content Skeleton */}
                <div className="space-y-3">
                  {/* Title */}
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-4 bg-gray-200 rounded w-1/2"></div>

                  {/* Price */}
                  <div className="h-6 bg-gray-200 rounded w-1/3"></div>

                  {/* Actions */}
                  <div className="flex items-center gap-3 mt-4">
                    <div className="h-8 bg-gray-200 rounded w-16"></div>
                    <div className="h-10 bg-gray-200 rounded flex-1"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          // Products Grid
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {currentProducts.map((product) => (
              <div
                key={product.id}
                className="transform hover:scale-105 transition-transform duration-300"
              >
                <ProductCard product={product} />
              </div>
            ))}
          </div>
        )}

        {/* Pagination */}
        {totalProducts > 0 && totalPages > 1 && (
          <div className="flex flex-col sm:flex-row items-center justify-between mt-12 gap-4">
            {/* Pagination Info */}
            <div className="text-sm text-gray-600">
              Page <span className="font-semibold">{currentPage}</span> of{' '}
              <span className="font-semibold">{totalPages}</span>
            </div>

            {/* Pagination Controls */}
            <div className="flex items-center gap-2">
              {/* Previous Button */}
              <Button
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <FaChevronLeft className="w-3 h-3" />
                Previous
              </Button>

              {/* Page Numbers */}
              <div className="flex items-center gap-1">
                {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
                  // Show first page, last page, current page, and pages around current
                  const showPage =
                    page === 1 ||
                    page === totalPages ||
                    (page >= currentPage - 1 && page <= currentPage + 1)

                  if (!showPage) {
                    // Show ellipsis for gaps
                    if (page === currentPage - 2 || page === currentPage + 2) {
                      return (
                        <span key={page} className="px-2 py-1 text-gray-400">
                          ...
                        </span>
                      )
                    }
                    return null
                  }

                  return (
                    <button
                      key={page}
                      onClick={() => setCurrentPage(page)}
                      className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                        currentPage === page
                          ? 'bg-primary text-white'
                          : 'text-gray-700 hover:bg-gray-100'
                      }`}
                    >
                      {page}
                    </button>
                  )
                })}
              </div>

              {/* Next Button */}
              <Button
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                Next
                <FaChevronRight className="w-3 h-3" />
              </Button>
            </div>

            {/* Products per page info */}
            <div className="text-sm text-gray-600">
              {productsPerPage} per page
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default Products
