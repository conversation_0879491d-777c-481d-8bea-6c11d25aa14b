import React, {useState, useRef, useEffect} from 'react'
import { IoSearchOutline } from "react-icons/io5";
import { FaUser, FaShoppingCart, FaChevronDown, FaSignOutAlt, FaUserCircle, FaClipboardList } from 'react-icons/fa';
import {Link, useNavigate} from 'react-router-dom'
import { FaBars } from "react-icons/fa";
import { RxCross2 } from "react-icons/rx";
import logo from '../../assets/Logo1.png'
import useCartStore from '../../store/cartStore'
import useAuthStore from '../../store/authStore'
import useProductStore from '../../store/productStore'
import HeaderSearch from './HeaderSearch'
import CartModal from './cart/CartModal'
import LoginPromptModal from '../../shared/ui/LoginPromptModal'
import CategoryDropdown from './CategoryDropdown'
import { toast } from 'sonner'

const Header = () => {
    const [isOpen, setIsOpen] = useState(false);
    const [showMobileSearch, setShowMobileSearch] = useState(false);
    const [showAccountDropdown, setShowAccountDropdown] = useState(false);
    const [showCartModal, setShowCartModal] = useState(false);
    const [showLoginPrompt, setShowLoginPrompt] = useState(false);
    const totalQuantity = useCartStore((state) => state.getTotalQuantity());
    const { currentUser, isAuthenticated, logout } = useAuthStore();
    const { setSelectedCategory } = useProductStore();
    const navigate = useNavigate();
    const dropdownRef = useRef(null);

    // Animation state for cart badge
    const [cartBadgeAnimate, setCartBadgeAnimate] = useState(false);
    const prevQuantityRef = useRef(totalQuantity);

    // Animate cart badge when quantity changes
    useEffect(() => {
        if (totalQuantity > prevQuantityRef.current) {
            setCartBadgeAnimate(true);
            const timer = setTimeout(() => setCartBadgeAnimate(false), 600);
            return () => clearTimeout(timer);
        }
        prevQuantityRef.current = totalQuantity;
    }, [totalQuantity]);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                setShowAccountDropdown(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);



    const handleLogout = () => {
        logout();
        setShowAccountDropdown(false);
        navigate('/');
    }

    // Handle category selection from dropdown
    const handleCategoryClick = (category) => {
        if (category === 'all') {
            setSelectedCategory(null); // Set to null for "All Products"
        } else {
            setSelectedCategory(category); // Set the specific category
        }
    }

    const toggleAccountDropdown = () => {
        setShowAccountDropdown(!showAccountDropdown);
    }

    const toggleCartModal = () => {
        if (!isAuthenticated) {
            setShowLoginPrompt(true);
            return;
        }
        setShowCartModal(!showCartModal);
    }

    return (
        <header className="bg-background w-full shadow-md">
            {/* Top Header Section - Logo, Search, Account, Cart */}
            <div className="px-6 py-2">
                {/* Desktop View */}
                <div className="hidden w-full lg:grid lg:grid-cols-3 items-center gap-6 px-4">
                    {/* Logo */}
                    <div className="flex items-center justify-start">
                        <Link to="/" className="flex items-center space-x-1 cursor-pointer">
                            <img src={logo} alt="ShringarHub" className='w-20 h-20 object-contain rounded-full' />
                        </Link>
                    </div>

                    {/* Center: Search Input */}
                    <div className="flex justify-center">
                        <HeaderSearch className="w-full max-w-xl" />
                    </div>

                    {/* User Account & Cart */}
                    <div className="flex items-center justify-end gap-6">
                        {/* Account Dropdown */}
                        <div className="relative" ref={dropdownRef}>
                            <div
                                className="flex items-center justify-center text-lg font-semibold gap-2 cursor-pointer hover:text-primary transition-colors"
                                onClick={toggleAccountDropdown}
                            >
                                <FaUser className='w-6 h-6' />
                                <span>My Account</span>
                                <FaChevronDown className={`w-4 h-4 transition-transform duration-200 ${showAccountDropdown ? 'rotate-180' : ''}`} />
                            </div>

                            {/* Dropdown Menu */}
                            {showAccountDropdown && (
                                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                                    {isAuthenticated ? (
                                        // Logged in user options
                                        <>
                                            <div className="px-4 py-2 border-b border-gray-100">
                                                <p className="text-sm text-gray-600">Welcome back!</p>
                                                <p className="text-sm font-semibold text-primary truncate">{currentUser?.email}</p>
                                            </div>
                                            <Link
                                                to="/account/profile"
                                                className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors"
                                                onClick={() => setShowAccountDropdown(false)}
                                            >
                                                <FaUserCircle className="w-4 h-4" />
                                                <span>Profile</span>
                                            </Link>
                                            <Link
                                                to="/account/orders"
                                                className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors"
                                                onClick={() => setShowAccountDropdown(false)}
                                            >
                                                <FaClipboardList className="w-4 h-4" />
                                                <span>Orders</span>
                                            </Link>

                                            <div className="border-t border-gray-100 mt-1 pt-1">
                                                <button
                                                    onClick={handleLogout}
                                                    className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors w-full text-left"
                                                >
                                                    <FaSignOutAlt className="w-4 h-4" />
                                                    <span>Logout</span>
                                                </button>
                                            </div>
                                        </>
                                    ) : (
                                        // Not logged in options
                                        <>
                                            <Link
                                                to="/login"
                                                className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors"
                                                onClick={() => setShowAccountDropdown(false)}
                                            >
                                                <FaUser className="w-4 h-4" />
                                                <span>Login</span>
                                            </Link>
                                            <Link
                                                to="/register"
                                                className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors"
                                                onClick={() => setShowAccountDropdown(false)}
                                            >
                                                <FaUserCircle className="w-4 h-4" />
                                                <span>Register</span>
                                            </Link>
                                        </>
                                    )}
                                </div>
                            )}
                        </div>
                        <div className="relative">
                            <button
                                onClick={toggleCartModal}
                                className="flex items-center space-x-2 hover:text-primary cursor-pointer transition-colors duration-200 text-lg font-semibold focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 rounded-lg px-2 py-1"
                                aria-label={`Shopping cart with ${totalQuantity} items`}
                            >
                                <div className="relative">
                                    <FaShoppingCart className='w-6 h-6' />
                                    {totalQuantity > 0 && (
                                        <span
                                            className={`absolute -top-2 -right-2 bg-primary text-white text-xs font-bold rounded-full min-w-[20px] h-5 flex items-center justify-center px-1 border-2 border-white shadow-lg transition-all duration-300 ${
                                                cartBadgeAnimate ? 'animate-bounce scale-110' : ''
                                            }`}
                                            aria-label={`${totalQuantity} items in cart`}
                                        >
                                            {totalQuantity > 99 ? '99+' : totalQuantity}
                                        </span>
                                    )}
                                </div>
                                <span>Cart</span>
                            </button>
                        </div>
                    </div>
                </div>

                {/* Mobile View */}
                <div className="flex lg:hidden w-full items-center justify-between">
                    {/* Left - Menu Icon */}
                    <div className="flex items-center gap-2 font-bold text-text cursor-pointer">
                        <FaBars className="text-xl md:text-2xl" onClick={() => setIsOpen(true)} />
                        <span className="text-md md:text-xl">Menu</span>
                    </div>
                    {/* Center - Logo */}
                    <div className="text-xl sm:text-2xl md:text-3xl font-serif font-bold text-primary">
                        <Link to="/" className="flex items-center space-x-1 cursor-pointer">
                            <img src={logo} alt="ShringarHub" className='w-32 h-32 object-contain rounded-full' />
                        </Link>
                    </div>
                    {/* Right - Account, Search & Cart Icons */}
                    <div className="flex relative gap-4 text-text justify-center items-center">
                        <button
                            onClick={() => setShowMobileSearch(!showMobileSearch)}
                            className="flex items-center space-x-1 hover:text-primary cursor-pointer"
                        >
                            <IoSearchOutline className="text-xl md:text-2xl" />
                        </button>
                        <div className="relative">
                            <button
                                onClick={toggleAccountDropdown}
                                className="flex items-center space-x-1 hover:text-primary cursor-pointer"
                            >
                                <FaUser className="text-xl md:text-2xl" />
                            </button>

                            {/* Mobile Account Dropdown */}
                            {showAccountDropdown && (
                                <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                                    {isAuthenticated ? (
                                        // Logged in user options
                                        <>
                                            <div className="px-4 py-2 border-b border-gray-100">
                                                <p className="text-xs text-gray-600">Welcome back!</p>
                                                <p className="text-xs font-semibold text-primary truncate">{currentUser?.email}</p>
                                            </div>
                                            <Link
                                                to="/account/profile"
                                                className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors text-sm"
                                                onClick={() => setShowAccountDropdown(false)}
                                            >
                                                <FaUserCircle className="w-4 h-4" />
                                                <span>Profile</span>
                                            </Link>
                                            <Link
                                                to="/account/orders"
                                                className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors text-sm"
                                                onClick={() => setShowAccountDropdown(false)}
                                            >
                                                <FaClipboardList className="w-4 h-4" />
                                                <span>Orders</span>
                                            </Link>

                                            <div className="border-t border-gray-100 mt-1 pt-1">
                                                <button
                                                    onClick={handleLogout}
                                                    className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-red-600 transition-colors w-full text-left text-sm"
                                                >
                                                    <FaSignOutAlt className="w-4 h-4" />
                                                    <span>Logout</span>
                                                </button>
                                            </div>
                                        </>
                                    ) : (
                                        // Not logged in options
                                        <>
                                            <Link
                                                to="/login"
                                                className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors text-sm"
                                                onClick={() => setShowAccountDropdown(false)}
                                            >
                                                <FaUser className="w-4 h-4" />
                                                <span>Login</span>
                                            </Link>
                                            <Link
                                                to="/register"
                                                className="flex items-center gap-3 px-4 py-2 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors text-sm"
                                                onClick={() => setShowAccountDropdown(false)}
                                            >
                                                <FaUserCircle className="w-4 h-4" />
                                                <span>Register</span>
                                            </Link>
                                        </>
                                    )}
                                </div>
                            )}
                        </div>
                        <button
                            onClick={toggleCartModal}
                            className="relative hover:text-primary cursor-pointer transition-colors duration-200 p-2 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50 rounded-lg"
                            aria-label={`Shopping cart with ${totalQuantity} items`}
                        >
                            <FaShoppingCart className="text-xl md:text-2xl" />
                            {totalQuantity > 0 && (
                                <span
                                    className={`absolute -top-1 -right-1 bg-primary text-white text-xs font-bold rounded-full min-w-[18px] h-[18px] flex items-center justify-center px-1 border-2 border-white shadow-lg transition-all duration-300 ${
                                        cartBadgeAnimate ? 'animate-bounce scale-110' : ''
                                    }`}
                                    aria-label={`${totalQuantity} items in cart`}
                                >
                                    {totalQuantity > 99 ? '99+' : totalQuantity}
                                </span>
                            )}
                        </button>
                    </div>
                </div>
            </div>

            {/* Navigation Bar Section - Categories and Links */}
            <nav className="bg-primary text-white shadow-md px-6 py-4 w-full">
                <div className="hidden lg:flex items-center justify-center gap-4 w-full">
                    {/* Left - Empty space */}
                    {/* <div></div> */}

                    {/* Center - Navigation Links with Categories Dropdown */}
                    <div className="flex items-center gap-8">
                        <Link to="/" className="text-white text-lg font-bold hover:text-secondary hover:scale-105 transition-all duration-200">
                            Home
                        </Link>
                        <CategoryDropdown onCategoryClick={handleCategoryClick} />
                        <Link to="/about" className="text-white text-lg font-bold hover:text-secondary hover:scale-105 transition-all duration-200">
                            About Us
                        </Link>
                        <Link to="/contact" className="text-white text-lg font-bold hover:text-secondary hover:scale-105 transition-all duration-200">
                            Contact Us
                        </Link>
                    </div>

                    {/* Right - Additional space or promotional text */}
                    {/* <div className="text-sm">
                        <span>Free Shipping on Orders Over ₹500</span>
                    </div> */}
                </div>
            </nav>
            
            {/* Mobile Search Bar - Appears when search icon is clicked */}
            {showMobileSearch && (
                <div className="lg:hidden w-full mt-3 px-4 pb-3">
                    <HeaderSearch className="w-full" />
                </div>
            )}
            
            {/* Mobile Side Panel Menu */}
            {isOpen && (
                <>
                    {/* Overlay */}
                    <div 
                        className="fixed inset-0 bg-black bg-opacity-50 z-40"
                        onClick={() => setIsOpen(false)}
                    ></div>
                    
                    {/* Side Panel */}
                    <div className="fixed top-0 left-0 h-full w-64 bg-background shadow-lg z-50 transform transition-transform duration-300 ease-in-out">
                        {/* Header with close button */}
                        <div className="flex justify-between items-center p-4 border-b border-secondary">
                            <h2 className="text-lg font-bold text-primary">Menu</h2>
                            <button onClick={() => setIsOpen(false)} className="text-text hover:text-primary">
                                <RxCross2 className="w-6 h-6" />
                            </button>
                        </div>
                        
                        {/* Menu Items */}
                        <div className="flex flex-col p-4">
                            <Link to="/" className="py-3 px-2 text-text hover:text-primary border-b border-secondary" onClick={() => setIsOpen(false)}>Home</Link>
                            <Link to="/products" className="py-3 px-2 text-text hover:text-primary border-b border-secondary" onClick={() => setIsOpen(false)}>All Categories</Link>
                            <Link to="/about" className="py-3 px-2 text-text hover:text-primary border-b border-secondary" onClick={() => setIsOpen(false)}>About Us</Link>
                            <Link to="/contact" className="py-3 px-2 text-text hover:text-primary border-b border-secondary" onClick={() => setIsOpen(false)}>Contact Us</Link>

                            {/* Account Section in Mobile Menu */}
                            <div className="mt-4 pt-4 border-t border-secondary">
                                <h3 className="text-primary font-bold mb-2">Account</h3>
                                {isAuthenticated ? (
                                    <>
                                        <div className="px-2 py-1 mb-2">
                                            <p className="text-xs text-gray-600">Welcome back!</p>
                                            <p className="text-xs font-semibold text-primary truncate">{currentUser?.email}</p>
                                        </div>
                                        <Link to="/account/profile" className="flex items-center gap-2 py-2 px-2 text-text hover:text-primary" onClick={() => setIsOpen(false)}>
                                            <FaUserCircle className="w-4 h-4" />
                                            <span>Profile</span>
                                        </Link>
                                        <Link to="/account/orders" className="flex items-center gap-2 py-2 px-2 text-text hover:text-primary" onClick={() => setIsOpen(false)}>
                                            <FaClipboardList className="w-4 h-4" />
                                            <span>Orders</span>
                                        </Link>

                                        <button
                                            onClick={() => {
                                                handleLogout();
                                                setIsOpen(false);
                                            }}
                                            className="flex items-center gap-2 py-2 px-2 text-text hover:text-red-600 w-full text-left"
                                        >
                                            <FaSignOutAlt className="w-4 h-4" />
                                            <span>Logout</span>
                                        </button>
                                    </>
                                ) : (
                                    <>
                                        <Link to="/login" className="flex items-center gap-2 py-2 px-2 text-text hover:text-primary" onClick={() => setIsOpen(false)}>
                                            <FaUser className="w-4 h-4" />
                                            <span>Login</span>
                                        </Link>
                                        <Link to="/register" className="flex items-center gap-2 py-2 px-2 text-text hover:text-primary" onClick={() => setIsOpen(false)}>
                                            <FaUserCircle className="w-4 h-4" />
                                            <span>Register</span>
                                        </Link>
                                    </>
                                )}
                            </div>

                            {/* Categories Section in Mobile Menu */}
                            <div className="mt-4 pt-4 border-t border-secondary">
                                <CategoryDropdown
                                    isMobile={true}
                                    onCategoryClick={(category) => {
                                        handleCategoryClick(category);
                                        setIsOpen(false);
                                    }}
                                />
                            </div>
                        </div>
                    </div>
                </>
            )}

            {/* Cart Modal */}
            <CartModal
                isOpen={showCartModal}
                onClose={() => setShowCartModal(false)}
            />

            {/* Login Prompt Modal */}
            <LoginPromptModal
                isOpen={showLoginPrompt}
                onClose={() => setShowLoginPrompt(false)}
                action="access your cart"
            />
        </header>
    )
}

export default Header
