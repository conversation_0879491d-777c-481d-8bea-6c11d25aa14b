import React from 'react'
import <PERSON><PERSON> from '../../../shared/ui/Button'
import { Link } from 'react-router-dom'

const Hero = () => {
  return (
    <section className="bg-gradient-to-br from-background via-accent to-secondary min-h-[80vh] flex items-center">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-20">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          
          {/* Text Content */}
          <div className="space-y-8 text-center lg:text-left">
            <div className="space-y-4">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl font-bold text-primary leading-tight">
                Welcome to{' '}
                <span className="bg-gradient-to-r from-primary to-red-600 bg-clip-text text-transparent">
                  ShringarHub
                </span>
              </h1>
              <p className="text-xl sm:text-2xl text-text/80 font-medium">
                Your Divine Marketplace for Sacred Items
              </p>
            </div>
            
            <div className="space-y-6">
              <p className="text-lg text-text/70 leading-relaxed max-w-2xl">
                Discover an exquisite collection of divine items, from beautiful Krishna statues and ornate poshaks 
                to sacred brass items and traditional jewelry. Each piece is carefully curated to bring spirituality 
                and devotion into your home and heart.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
                <Link to="/products">
                  <Button 
                    variant="primary" 
                    size="lg" 
                    className="w-full sm:w-auto px-8 py-4 text-lg font-semibold rounded-full shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300"
                  >
                    Explore Collections
                  </Button>
                </Link>
                <Link to="/about">
                  <Button 
                    variant="outline" 
                    size="lg" 
                    className="w-full sm:w-auto px-8 py-4 text-lg font-semibold rounded-full hover:bg-primary hover:text-white transition-all duration-300"
                  >
                    Learn More
                  </Button>
                </Link>
              </div>
            </div>
          </div>

          {/* Image Content */}
          <div className="relative">
            <div className="relative z-10">
              <img 
                src="/images/Krishna.jpeg" 
                alt="Divine Krishna" 
                className="w-full h-[400px] sm:h-[500px] lg:h-[600px] object-cover rounded-3xl shadow-2xl"
              />
              {/* Decorative overlay */}
              <div className="absolute inset-0 bg-gradient-to-t from-primary/20 to-transparent rounded-3xl"></div>
            </div>
            
            {/* Decorative elements */}
            <div className="absolute -top-6 -right-6 w-24 h-24 bg-primary/10 rounded-full blur-xl"></div>
            <div className="absolute -bottom-6 -left-6 w-32 h-32 bg-secondary/20 rounded-full blur-xl"></div>
          </div>
          
        </div>
      </div>
    </section>
  )
}

export default Hero
