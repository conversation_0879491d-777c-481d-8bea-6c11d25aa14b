import React from 'react'
import { <PERSON> } from 'react-router-dom'
import { encodeCategoryForURL } from '../../../utils/categoryUtils'

const Categories = () => {
  const categories = [
    {
      name: "Statue",
      image: "https://tinyurl.com/ywmwsxra",
      count: "15+ Items"
    },
    {
      name: "Poshak",
      image: "https://tinyurl.com/2dkusuzb",
      count: "20+ Items"
    },
    {
      name: "Brass Items",
      image: "https://tinyurl.com/2v7svhfa",
      count: "25+ Items"
    },
    {
      name: "Jwellery Set",
      image: "https://tinyurl.com/4nkjtkes",
      count: "18+ Items"
    },
    {
      name: "<PERSON><PERSON><PERSON>",
      image: "https://tinyurl.com/5566pccy",
      count: "10+ Items"
    },
    {
      name: "<PERSON><PERSON>",
      image: "https://tinyurl.com/fexjcxk3",
      count: "12+ Items"
    }
  ]

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4">
        <div className="text-center mb-12">
          <h2 className="text-3xl md:text-4xl font-bold text-text mb-4 font-serif">Shop by Category</h2>
          <p className="text-gray-600 text-lg">Discover our divine collection</p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
          {categories.map((category, index) => (
            <Link
              key={index}
              to={`/category/${encodeCategoryForURL(category.name)}`}
              className="group text-center hover:transform hover:scale-105 transition-all duration-300"
            >
              <div className="bg-accent rounded-full p-4 mb-4 mx-auto w-20 h-20 sm:w-24 sm:h-24 flex items-center justify-center group-hover:bg-secondary transition-colors duration-300">
                <img
                  src={category.image}
                  alt={category.name}
                  className="w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-full"
                />
              </div>
              <h3 className="font-semibold text-text mb-1 font-serif text-sm sm:text-base">{category.name}</h3>
              <p className="text-xs text-gray-500">{category.count}</p>
            </Link>
          ))}
        </div>

        {/* View All Categories Button */}
        <div className="text-center mt-12">
          <Link 
            to="/products"
            className="inline-flex items-center px-6 py-3 sm:px-8 sm:py-4 bg-primary text-white font-semibold rounded-full hover:bg-red-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl text-sm sm:text-base"
          >
            View All Products
            <svg className="ml-2 w-4 h-4 sm:w-5 sm:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default Categories
