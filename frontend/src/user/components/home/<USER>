import React from 'react'
import { FaShieldAlt, FaUndo, FaGem, FaShippingFast } from 'react-icons/fa'

const Features = () => {
  const features = [
    {
      icon: FaShieldAlt,
      title: "Secure Payment",
      description: "100% secure payment with SSL encryption and trusted payment gateways",
      color: "text-green-600",
      bgColor: "bg-green-50"
    },
    {
      icon: FaUndo,
      title: "Easy Returns",
      description: "Hassle-free 30-day return policy for your complete satisfaction",
      color: "text-blue-600",
      bgColor: "bg-blue-50"
    },
    {
      icon: FaGem,
      title: "Quality Products",
      description: "Authentic and premium quality divine items sourced from trusted artisans",
      color: "text-purple-600",
      bgColor: "bg-purple-50"
    },
    {
      icon: FaShippingFast,
      title: "Free Shipping",
      description: "Free shipping on orders over ₹500 across India with fast delivery",
      color: "text-orange-600",
      bgColor: "bg-orange-50"
    }
  ]

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-primary mb-4">
            Why Choose ShringarHub?
          </h2>
          <p className="text-lg text-text/70 max-w-2xl mx-auto">
            We're committed to providing you with the best shopping experience for your spiritual needs
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
          {features.map((feature, index) => {
            const IconComponent = feature.icon
            return (
              <div 
                key={index}
                className="group text-center p-6 rounded-2xl hover:shadow-lg transition-all duration-300 hover:-translate-y-2"
              >
                <div className={`inline-flex items-center justify-center w-16 h-16 ${feature.bgColor} rounded-full mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  <IconComponent className={`w-8 h-8 ${feature.color}`} />
                </div>
                <h3 className="text-xl font-semibold text-text mb-3">
                  {feature.title}
                </h3>
                <p className="text-text/70 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            )
          })}
        </div>
      </div>
    </section>
  )
}

export default Features
