import React, { useState, useRef, useEffect } from 'react'
import { Link } from 'react-router-dom'
import { FaBars, FaChevronDown, FaChevronUp } from 'react-icons/fa'
import { getUniqueCategories, encodeCategoryForURL } from '../../utils/categoryUtils'
import useProductStore from '../../store/productStore'

const CategoryDropdown = ({ isMobile = false, onCategoryClick = () => {} }) => {
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef(null)
  const categories = getUniqueCategories()

  // Get functions from product store
  const { clearSearch, setSelectedCategory, clearAllFilters } = useProductStore()

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  const toggleDropdown = () => {
    setIsOpen(!isOpen)
  }

  const handleCategorySelect = (category) => {
    console.log('CategoryDropdown: Selecting category:', category); // Debug log
    clearSearch() // Clear search query when selecting any category
    setSelectedCategory(category) // Update the store directly
    setIsOpen(false)
    onCategoryClick(category) // Also call the prop function
  }

  // Handle "All Products" click - clear all filters to ensure clean state
  const handleAllProductsClick = () => {
    console.log('CategoryDropdown: Selecting all products'); // Debug log
    clearAllFilters() // Clear ALL filters including search, category, price, etc.
    setIsOpen(false)
    onCategoryClick('all') // Also call the prop function
  }

  if (isMobile) {
    // Mobile version - simple list
    return (
      <div className="space-y-2">
        <div className="flex items-center gap-2 py-2 px-2 text-text font-semibold">
          <FaBars className="w-4 h-4" />
          <span>Categories</span>
        </div>
        <div className="pl-6 space-y-1">
          <Link
            to="/products"
            className="block py-2 px-2 text-text hover:text-primary transition-colors text-sm"
            onClick={handleAllProductsClick}
          >
            All Products
          </Link>
          {categories.map((category) => (
            <Link
              key={category}
              to={`/category/${encodeCategoryForURL(category)}`}
              className="block py-2 px-2 text-text hover:text-primary transition-colors text-sm"
              onClick={() => handleCategorySelect(category)}
            >
              {category}
            </Link>
          ))}
        </div>
      </div>
    )
  }

  // Desktop version - dropdown
  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={toggleDropdown}
        className="flex text-lg gap-4 items-center font-bold cursor-pointer hover:text-secondary hover:scale-105 transition-all duration-200"
      >
        <span>Categories</span>
        {isOpen ? (
          <FaChevronUp className="w-4 h-4" />
        ) : (
          <FaChevronDown className="w-4 h-4" />
        )}
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50 max-h-96 overflow-y-auto">
          {/* All Products Option */}
          <Link
            to="/products"
            className="flex items-center gap-3 px-4 py-3 text-gray-700 hover:bg-gray-50 hover:text-primary transition-colors border-b border-gray-100"
            onClick={handleAllProductsClick}
          >
            {/* <FaBars className="w-4 h-4 text-gray-500" /> */}
            <span className="font-medium">All Products</span>
          </Link>

          {/* Category Options */}
          {categories.map((category) => (
            <Link
              key={category}
              to={`/category/${encodeCategoryForURL(category)}`}
              className="block px-4 py-3 text-gray-700  hover:underline hover:text-primary transition-colors"
              onClick={() => handleCategorySelect(category)}
            >
              <span className="font-medium">{category}</span>
            </Link>
          ))}

          {/* Footer */}
          <div className="border-t border-gray-100 mt-2 pt-2">
            <div className="px-4 py-2 text-xs text-gray-500 text-center">
              {categories.length} categories available
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default CategoryDropdown
