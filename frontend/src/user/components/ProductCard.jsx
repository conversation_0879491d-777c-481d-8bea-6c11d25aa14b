import React, {useState} from 'react'
import Button from '../../shared/ui/Button';
import { FaCartPlus } from 'react-icons/fa';
import {useNavigate} from 'react-router-dom'
import useCartStore from '../../store/cartStore'; // import store
import { generateProductUrl } from '../../utils/urlUtils';
import { toast } from 'sonner';

// Memoize quantity options to prevent recreation on every render
const QUANTITY_OPTIONS = Array.from({ length: 10 }, (_, i) => i + 1);

const ProductCard = ({ product}) => {
    const navigate = useNavigate();
    const addToCart = useCartStore(state => state.addToCart);

    const [quantity, setQuantity] = useState(1);

    const handleAddToCart = () => {
      addToCart(product, quantity);
      // Add toast notification for user feedback
      toast.success(`${product.name} added to cart!`, {
        description: `Quantity: ${quantity}`,
        duration: 2000,
      });
    }

    const handleClick = () => {
      const productUrl = generateProductUrl(product)
      navigate(productUrl)
    }

    const handleKeyDown = (e) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        handleClick();
      }
    }



  return (
    <div className="group bg-white rounded-3xl shadow-lg hover:shadow-2xl border border-gray-100 p-4 flex flex-col w-full min-h-[420px] transition-all duration-300 hover:-translate-y-2 relative overflow-hidden">


      {/* Product Image Container - Clickable */}
      <div
        className="relative w-full h-48 sm:h-56 lg:h-48 xl:h-56 mb-4 overflow-hidden rounded-2xl cursor-pointer"
        onClick={handleClick}
        onKeyDown={handleKeyDown}
        role="button"
        tabIndex={0}
        aria-label={`View details for ${product.name}`}
      >
        <img
          src={product.image}
          alt={product.name || 'Product image'}
          className="w-full h-full object-contain transition-transform duration-500 group-hover:scale-105"
          loading="lazy"
        />


      </div>

      {/* Product Info */}
      <div className="flex-1 flex flex-col justify-between">
        <div>
          <h3
            className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 hover:text-primary transition-colors duration-200 cursor-pointer"
            onClick={handleClick}
            onKeyDown={handleKeyDown}
            role="button"
            tabIndex={0}
            title={product.name}
            aria-label={`View details for ${product.name}`}
          >
            {product.name}
          </h3>
          <div className="mb-3">
            <span className="text-2xl font-bold text-primary">₹{product.price.toFixed(2)}</span>
          </div>
        </div>

        {/* Action Buttons - Responsive Layout */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3">
          {/* Quantity Selector - Full width on mobile */}
          <div className="flex items-center gap-2 w-full sm:w-auto">
            <label
              htmlFor={`quantity-${product.id}`}
              className="text-sm font-medium text-gray-700 whitespace-nowrap"
            >
              Qty:
            </label>
            <select
              id={`quantity-${product.id}`}
              value={quantity}
              onChange={(e) => {
                e.stopPropagation();
                setQuantity(Number(e.target.value));
              }}
              className="flex-1 sm:flex-none px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-primary focus:border-primary sm:min-w-[60px]"
              aria-label={`Select quantity for ${product.name}`}
            >
              {QUANTITY_OPTIONS.map((num) => (
                <option key={num} value={num}>{num}</option>
              ))}
            </select>
          </div>

          {/* Add to Cart Button */}
          <Button
            onClick={(e) => {
              e.stopPropagation();
              handleAddToCart();
            }}
            variant="primary"
            className="flex-1 py-2.5 px-4 rounded-xl font-semibold transition-all duration-300 hover:shadow-lg flex items-center justify-center gap-2 min-h-[40px]"
            aria-label={`Add ${product.name} to cart`}
          >
            <FaCartPlus className="w-4 h-4 flex-shrink-0 hidden sm:inline md:hidden" />
            <span className="sm:hidden md:inline">Add to Cart</span>
          </Button>
        </div>
      </div>
    </div>
  )
}

export default ProductCard
