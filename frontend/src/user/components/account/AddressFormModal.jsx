import React, { useState, useEffect } from 'react'
import { toast } from 'sonner'
import Modal from '../../../shared/ui/Modal'
import Input from '../../../shared/ui/Input'
import Button from '../../../shared/ui/Button'

// Indian states list with PIN code patterns
const indianStates = [
  'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh', 'Goa', 'Gujarat',
  'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka', 'Kerala', 'Madhya Pradesh',
  'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram', 'Nagaland', 'Odisha', 'Punjab',
  'Rajasthan', 'Sikkim', 'Tamil Nadu', 'Telangana', 'Tripura', 'Uttar Pradesh',
  'Uttarakhand', 'West Bengal', 'Andaman and Nicobar Islands', 'Chandigarh',
  'Dadra and Nagar Haveli and Daman and Diu', 'Delhi', 'Jammu and Kashmir', 'Ladakh',
  'Lakshadweep', 'Puducherry'
]

// PIN code patterns for Indian states
const statePinCodePatterns = {
  'Andhra Pradesh': /^5[0-3]\d{4}$/,
  'Arunachal Pradesh': /^79[0-1]\d{3}$/,
  'Assam': /^78[0-8]\d{3}$/,
  'Bihar': /^8[0-5]\d{4}$/,
  'Chhattisgarh': /^49[0-7]\d{3}$/,
  'Goa': /^40[3-4]\d{3}$/,
  'Gujarat': /^3[6-9]\d{4}$/,
  'Haryana': /^1[2-3]\d{4}$/,
  'Himachal Pradesh': /^1[7-9]\d{4}$/,
  'Jharkhand': /^8[1-3]\d{4}$/,
  'Karnataka': /^5[6-9]\d{4}$/,
  'Kerala': /^6[7-9]\d{4}$/,
  'Madhya Pradesh': /^4[5-8]\d{4}$/,
  'Maharashtra': /^4[0-4]\d{4}$/,
  'Manipur': /^79[5-6]\d{3}$/,
  'Meghalaya': /^79[3-4]\d{3}$/,
  'Mizoram': /^79[6-7]\d{3}$/,
  'Nagaland': /^79[7-8]\d{3}$/,
  'Odisha': /^7[5-7]\d{4}$/,
  'Punjab': /^1[4-6]\d{4}$/,
  'Rajasthan': /^3[0-4]\d{4}$/,
  'Sikkim': /^737\d{3}$/,
  'Tamil Nadu': /^6[0-6]\d{4}$/,
  'Telangana': /^5[0-5]\d{4}$/,
  'Tripura': /^79[9]\d{3}$/,
  'Uttar Pradesh': /^2[0-8]\d{4}$/,
  'Uttarakhand': /^2[4-6]\d{4}$/,
  'West Bengal': /^7[0-4]\d{4}$/,
  'Andaman and Nicobar Islands': /^744\d{3}$/,
  'Chandigarh': /^160\d{3}$/,
  'Dadra and Nagar Haveli and Daman and Diu': /^39[6-7]\d{3}$/,
  'Delhi': /^11\d{4}$/,
  'Jammu and Kashmir': /^1[8-9]\d{4}$/,
  'Ladakh': /^19[4-5]\d{3}$/,
  'Lakshadweep': /^682\d{3}$/,
  'Puducherry': /^60[5-9]\d{3}$/
}

const AddressFormModal = ({ isOpen, onClose, onSave, initialData, title, mode = 'shipping' }) => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    addressLine1: '',
    addressLine2: '',
    city: '',
    state: '',
    zipCode: '',
    phoneNumber: '',
    isDefault: false
  })
  const [errors, setErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Reset form when modal opens/closes or initialData changes
  useEffect(() => {
    if (isOpen) {
      if (initialData) {
        // Handle both old format (fullName) and new format (firstName, lastName)
        if (initialData.fullName && !initialData.firstName && !initialData.lastName) {
          const [firstName = '', lastName = ''] = initialData.fullName.split(' ')
          setFormData({
            ...initialData,
            firstName,
            lastName
          })
        } else {
          setFormData(initialData)
        }
      } else {
        setFormData({
          firstName: '',
          lastName: '',
          addressLine1: '',
          addressLine2: '',
          city: '',
          state: '',
          zipCode: '',
          phoneNumber: '',
          isDefault: false
        })
      }
      setErrors({})
    }
  }, [isOpen, initialData])

  const handleChange = (e) => {
    // Prevent event bubbling that might close the modal
    e.stopPropagation()

    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    // Required fields
    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required'
    }
    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required'
    }
    if (!formData.addressLine1.trim()) {
      newErrors.addressLine1 = 'Address line 1 is required'
    }
    if (!formData.city.trim()) {
      newErrors.city = 'City is required'
    }
    if (!formData.state.trim()) {
      newErrors.state = 'State is required'
    }

    // ZIP Code validation (6-digit Indian PIN code with state-specific validation)
    if (!formData.zipCode.trim()) {
      newErrors.zipCode = 'ZIP code is required'
    } else if (!/^\d{6}$/.test(formData.zipCode)) {
      newErrors.zipCode = 'ZIP code must be 6 digits'
    } else if (formData.state && statePinCodePatterns[formData.state]) {
      // Validate PIN code against selected state's pattern
      if (!statePinCodePatterns[formData.state].test(formData.zipCode)) {
        newErrors.zipCode = `PIN code does not match ${formData.state}. Please enter a valid PIN code for this state.`
      }
    }

    // Phone number validation (10 digits only)
    if (!formData.phoneNumber.trim()) {
      newErrors.phoneNumber = 'Phone number is required'
    } else if (!/^\d{10}$/.test(formData.phoneNumber)) {
      newErrors.phoneNumber = 'Phone number must be exactly 10 digits'
    }

    return newErrors
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    e.stopPropagation() // Prevent event bubbling

    const validationErrors = validateForm()
    if (Object.keys(validationErrors).length > 0) {
      setErrors(validationErrors)

      // Show toast for validation errors
      const errorCount = Object.keys(validationErrors).length
      toast.error(`Please fix ${errorCount} validation error${errorCount > 1 ? 's' : ''} before saving.`)
      return
    }

    setIsSubmitting(true)

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 500))

      // Convert to the format expected by the address store (with fullName for backward compatibility)
      const addressData = {
        ...formData,
        fullName: `${formData.firstName} ${formData.lastName}`.trim()
      }

      onSave(addressData)
    } catch (error) {
      console.error('Error saving address:', error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Prevent modal from closing during form interaction
  const handleModalClose = () => {
    if (!isSubmitting) {
      onClose()
    }
  }

  return (
    <Modal isOpen={isOpen} onClose={handleModalClose} title={title}>
      <form onSubmit={handleSubmit} className="space-y-6">
        {/* Name Row */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <Input
            label="First Name"
            name="firstName"
            value={formData.firstName}
            onChange={handleChange}
            error={errors.firstName}
            placeholder="Enter first name"
            required
          />
          <Input
            label="Last Name"
            name="lastName"
            value={formData.lastName}
            onChange={handleChange}
            error={errors.lastName}
            placeholder="Enter last name"
            required
          />
        </div>

        {/* Address Line 1 */}
        <Input
          label="Address Line 1"
          name="addressLine1"
          value={formData.addressLine1}
          onChange={handleChange}
          error={errors.addressLine1}
          placeholder="House number, street name"
          required
        />

        {/* Address Line 2 */}
        <Input
          label="Address Line 2 (Optional)"
          name="addressLine2"
          value={formData.addressLine2}
          onChange={handleChange}
          error={errors.addressLine2}
          placeholder="Apartment, suite, unit, building, floor, etc."
        />

        {/* City and State Row */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <Input
            label="City"
            name="city"
            value={formData.city}
            onChange={handleChange}
            error={errors.city}
            placeholder="Enter city name"
            required
          />

          {/* State Dropdown */}
          <div className="flex flex-col gap-2">
            <label htmlFor="state" className="text-sm font-medium text-gray-700">
              State <span className="text-primary ml-0.5">*</span>
            </label>
            <select
              id="state"
              name="state"
              value={formData.state}
              onChange={handleChange}
              required
              className={`
                flex-1 px-4 py-2 border rounded-md border-gray-300
                text-black bg-white
                focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent
                ${errors.state ? 'border-red-500' : ''}
              `}
            >
              <option value="">Select a state</option>
              {indianStates.map((state) => (
                <option key={state} value={state}>
                  {state}
                </option>
              ))}
            </select>
            {errors.state && <p className="text-xs text-red-500">{errors.state}</p>}
          </div>
        </div>

        {/* ZIP Code and Phone Row */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          <Input
            label="ZIP Code"
            name="zipCode"
            value={formData.zipCode}
            onChange={handleChange}
            error={errors.zipCode}
            placeholder="123456"
            maxLength={6}
            required
          />

          {/* Phone Number with +91 Prefix */}
          <div className="flex flex-col gap-2">
            <label htmlFor="phoneNumber" className="text-sm font-medium text-gray-700">
              Phone Number <span className="text-primary ml-0.5">*</span>
            </label>
            <div className="flex">
              {/* +91 Prefix */}
              <div className="flex items-center px-3 py-2 bg-gray-100 border border-r-0 border-gray-300 rounded-l-md text-gray-700 font-medium">
                +91
              </div>
              {/* Phone Input */}
              <input
                type="tel"
                id="phoneNumber"
                name="phoneNumber"
                value={formData.phoneNumber}
                onChange={(e) => {
                  // Stop propagation to prevent modal closing
                  e.stopPropagation()

                  // Only allow digits and limit to 10 characters
                  const value = e.target.value.replace(/\D/g, '').slice(0, 10)

                  // Update form data directly
                  setFormData(prev => ({
                    ...prev,
                    phoneNumber: value
                  }))

                  // Clear error if exists
                  if (errors.phoneNumber) {
                    setErrors(prev => ({
                      ...prev,
                      phoneNumber: ''
                    }))
                  }
                }}
                placeholder="9876543210"
                maxLength={10}
                required
                className={`
                  flex-1 px-4 py-2 border rounded-r-md border-gray-300
                  text-black bg-white
                  focus:outline-none focus:ring-2 focus:ring-gray-400 focus:border-transparent
                  ${errors.phoneNumber ? 'border-red-500' : ''}
                `}
                aria-describedby={errors.phoneNumber ? 'phoneNumber-error' : undefined}
              />
            </div>
            {errors.phoneNumber && (
              <p id="phoneNumber-error" className="text-xs text-red-500">
                {errors.phoneNumber}
              </p>
            )}
            <p className="text-xs text-gray-500">Enter 10-digit mobile number</p>
          </div>
        </div>

        {/* Default Address Checkbox - Only show for shipping addresses */}
        {mode === 'shipping' && (
          <div className="flex items-center space-x-3 pt-2">
            <input
              type="checkbox"
              id="isDefault"
              name="isDefault"
              checked={formData.isDefault}
              onChange={handleChange}
              className="w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary focus:ring-2"
            />
            <label htmlFor="isDefault" className="text-sm font-medium text-gray-700">
              Set as default shipping address
            </label>
          </div>
        )}

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-3 pt-4">
          <Button
            type="button"
            variant="outline"
            onClick={handleModalClose}
            className="sm:order-1"
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          
          <Button
            type="submit"
            variant="primary"
            className="sm:order-2"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Save Address'}
          </Button>
        </div>
      </form>
    </Modal>
  )
}

export default AddressFormModal
