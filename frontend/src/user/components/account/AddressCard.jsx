import React from 'react'
import { FaEdit, FaTrash, Fa<PERSON><PERSON><PERSON> } from 'react-icons/fa'

const AddressCard = ({ address, onEdit, onDelete, onSetDefault }) => {
  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this address?')) {
      onDelete()
    }
  }

  return (
    <div className="bg-white border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
      {/* Address Content */}
      <div className="flex justify-between items-start">
        {/* Address Details */}
        <div className="flex-1">
          <div className="space-y-1 text-gray-900">
            <p className="font-semibold">{address.fullName}</p>
            <p>{address.addressLine1}</p>
            {address.addressLine2 && <p>{address.addressLine2}</p>}
            <p>{address.city}, {address.state} {address.zipCode}</p>
            <p className="text-gray-600">+91 {address.phoneNumber}</p>
          </div>

          {/* Default <PERSON>ge */}
          {address.isDefault && (
            <div className="mt-3">
              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                <FaCheck className="w-3 h-3 mr-1" />
                Default
              </span>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col space-y-2 ml-4">
          <button
            onClick={onEdit}
            className="flex items-center space-x-1 text-sm text-primary hover:text-primary-dark transition-colors"
          >
            <FaEdit className="w-3 h-3" />
            <span>Edit</span>
          </button>
          
          <button
            onClick={handleDelete}
            className="flex items-center space-x-1 text-sm text-red-600 hover:text-red-700 transition-colors"
          >
            <FaTrash className="w-3 h-3" />
            <span>Delete</span>
          </button>

          {!address.isDefault && (
            <button
              onClick={onSetDefault}
              className="text-sm text-gray-600 hover:text-gray-800 transition-colors"
            >
              Set as Default
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default AddressCard
