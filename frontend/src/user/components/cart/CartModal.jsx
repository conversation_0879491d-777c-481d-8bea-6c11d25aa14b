import React, { useEffect, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { RxCross2 } from 'react-icons/rx'
import { FaShoppingCart, FaTrash } from 'react-icons/fa'
import useCartStore from '../../../store/cartStore'
import useAuthStore from '../../../store/authStore'
import Button from '../../../shared/ui/Button'
import LoginPromptModal from '../../../shared/ui/LoginPromptModal'
import { generateProductUrl } from '../../../utils/urlUtils'

const CartModal = ({ isOpen, onClose }) => {
  const navigate = useNavigate()
  const { cartItems = [], getTotalQuantity, getSubtotal, increaseQuantity, decreaseQuantity, removeFromCart } = useCartStore()
  const { isAuthenticated } = useAuthStore()

  // Ensure cartItems is always an array
  const items = Array.isArray(cartItems) ? cartItems : []

  // Login prompt modal state
  const [showLoginPrompt, setShowLoginPrompt] = useState(false)
  const [loginAction, setLoginAction] = useState('')

  // Handle ESC key press and prevent background scroll
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape' && isOpen) {
        onClose()
      }
    }

    const handleWheel = (event) => {
      // Prevent wheel events from bubbling to the background
      event.preventDefault()
      event.stopPropagation()
    }

    const handleTouchMove = (event) => {
      // Prevent touch scroll on mobile
      event.preventDefault()
    }

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey)

      // Store original body styles
      const originalStyle = window.getComputedStyle(document.body)
      const originalOverflow = originalStyle.overflow
      const originalPaddingRight = originalStyle.paddingRight

      // Get scrollbar width
      const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth

      // Prevent body scroll and hide scrollbar completely
      document.body.style.overflow = 'hidden'
      document.body.style.paddingRight = `${scrollbarWidth}px`
      document.body.style.position = 'fixed'
      document.body.style.width = '100%'
      document.body.style.height = '100%'
      document.documentElement.style.overflow = 'hidden'

      // Prevent wheel and touch events on the entire document
      document.addEventListener('wheel', handleWheel, { passive: false })
      document.addEventListener('touchmove', handleTouchMove, { passive: false })

      // Store original values for cleanup
      document.body.dataset.originalOverflow = originalOverflow
      document.body.dataset.originalPaddingRight = originalPaddingRight
      document.body.dataset.originalPosition = originalStyle.position
      document.body.dataset.originalWidth = originalStyle.width
      document.body.dataset.originalHeight = originalStyle.height
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey)
      document.removeEventListener('wheel', handleWheel)
      document.removeEventListener('touchmove', handleTouchMove)

      // Restore original body and html styles
      if (document.body.dataset.originalOverflow !== undefined) {
        document.body.style.overflow = document.body.dataset.originalOverflow
        document.body.style.paddingRight = document.body.dataset.originalPaddingRight
        document.body.style.position = document.body.dataset.originalPosition
        document.body.style.width = document.body.dataset.originalWidth
        document.body.style.height = document.body.dataset.originalHeight
        document.documentElement.style.overflow = ''
        delete document.body.dataset.originalOverflow
        delete document.body.dataset.originalPaddingRight
        delete document.body.dataset.originalPosition
        delete document.body.dataset.originalWidth
        delete document.body.dataset.originalHeight
      }
    }
  }, [isOpen, onClose])

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      onClose()
    }
  }

  // Handle navigation with authentication check
  const handleViewCart = () => {
    if (!isAuthenticated) {
      setLoginAction('view your cart')
      setShowLoginPrompt(true)
      return
    }
    onClose()
    navigate('/cart')
  }

  const handleCheckout = () => {
    if (!isAuthenticated) {
      setLoginAction('proceed to checkout')
      setShowLoginPrompt(true)
      return
    }
    onClose()
    navigate('/checkout')
  }

  const handleContinueShopping = () => {
    onClose()
    navigate('/products')
  }

  const handleProductClick = (product) => {
    const productUrl = generateProductUrl(product)
    onClose() // Close the cart modal
    navigate(productUrl) // Navigate to product detail page
  }

  

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  if (!isOpen) return null

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex justify-end"
      onClick={handleBackdropClick}
      onWheel={(e) => {
        e.preventDefault()
        e.stopPropagation()
      }}
      onTouchMove={(e) => {
        e.preventDefault()
        e.stopPropagation()
      }}
      style={{
        touchAction: 'none' // Prevent touch scrolling
      }}
    >
      {/* Side Modal */}
      <div
        className={`
          bg-white h-full w-full max-w-md shadow-xl transform transition-transform duration-300 ease-in-out
          ${isOpen ? 'translate-x-0' : 'translate-x-full'}
        `}
        onClick={(e) => e.stopPropagation()}
        onWheel={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-gray-200">
          <div className="flex items-center space-x-2">
            <FaShoppingCart className="w-5 h-5 text-primary" />
            <h2 className="text-lg font-semibold text-gray-900">
              Shopping Cart ({getTotalQuantity ? getTotalQuantity() : 0})
            </h2>
          </div>
          <button
            onClick={onClose}
            className="p-1 hover:bg-gray-100 rounded-full transition-colors"
          >
            <RxCross2 className="w-5 h-5 text-gray-500 hover:text-gray-700" />
          </button>
        </div>

        {/* Cart Content */}
        <div className="flex flex-col h-full">
          {items.length === 0 ? (
            /* Empty Cart */
            <div className="flex-1 flex flex-col items-center justify-center text-center p-4">
              <FaShoppingCart className="w-16 h-16 text-gray-300 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Your cart is empty</h3>
              <p className="text-gray-600 mb-6">Continue shopping to add divine items to your cart!</p>
              <Button
                variant="primary"
                onClick={handleContinueShopping}
                className="px-6"
              >
                Continue Shopping
              </Button>
            </div>
          ) : (
            <>
              {/* Header Row - Fixed */}
              <div className="px-4 pt-4 pb-2">
                <div className="grid grid-cols-3 gap-4 pb-2 border-b border-gray-200">
                  <div className="col-span-2">
                    <h4 className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Product</h4>
                  </div>
                  <div className="text-right">
                    <h4 className="text-sm font-semibold text-gray-700 uppercase tracking-wide">Total</h4>
                  </div>
                </div>
              </div>

              {/* Scrollable Cart Items */}
              <div
                className="flex-1 px-4"
                style={{
                  overflowY: items.length > 3 ? 'auto' : 'visible', // Only show scroll when needed
                  scrollbarWidth: 'thin',
                  scrollbarColor: '#d1d5db transparent',
                  maxHeight: 'calc(100vh - 200px)' // Ensure proper height calculation
                }}
                onWheel={(e) => {
                  const target = e.currentTarget
                  const { scrollTop, scrollHeight, clientHeight } = target

                  // Only handle scroll if content actually overflows
                  if (scrollHeight > clientHeight) {
                    // Check if we're at scroll boundaries
                    const isAtTop = scrollTop === 0
                    const isAtBottom = scrollTop + clientHeight >= scrollHeight - 1

                    if ((e.deltaY < 0 && isAtTop) || (e.deltaY > 0 && isAtBottom)) {
                      // At boundary, prevent background scroll
                      e.preventDefault()
                    }
                    // Always stop propagation to prevent background scroll
                    e.stopPropagation()
                  } else {
                    // No overflow, prevent all scroll events
                    e.preventDefault()
                    e.stopPropagation()
                  }
                }}
              >
                <div className="space-y-4 pb-4">
                  {items.map((item) => (
                    <div key={item.id} className="grid grid-cols-3 gap-4 p-3 border border-gray-200 rounded-lg">
                      {/* Product Column (spans 2 columns) */}
                      <div className="col-span-2 flex space-x-4">
                        {/* Product Image - Larger size */}
                        <div className="flex-shrink-0">
                          <img
                            src={item.image}
                            alt={item.name}
                            className="w-20 h-20 object-cover rounded-lg border border-gray-200"
                            onError={(e) => {
                              e.target.src = 'https://via.placeholder.com/80x80?text=No+Image'
                            }}
                          />
                        </div>

                        {/* Product Details */}
                        <div className="flex-1 min-w-0 flex flex-col justify-between">
                          {/* Product Name - Clickable */}
                          <h4
                            className="text-sm font-medium text-gray-900 mb-1 leading-tight cursor-pointer hover:text-primary hover:underline transition-colors"
                            style={{
                              display: '-webkit-box',
                              WebkitLineClamp: 2,
                              WebkitBoxOrient: 'vertical',
                              overflow: 'hidden'
                            }}
                            onClick={() => handleProductClick(item)}
                            title="Click to view product details"
                          >
                            {item.name}
                          </h4>

                          {/* Product Price */}
                          <div className="text-sm text-gray-600 mb-3">
                            {formatCurrency(item.price)} each
                          </div>

                          {/* Quantity Controls */}
                          <div className="flex items-center space-x-2">
                            <button
                              onClick={() => decreaseQuantity(item.id)}
                              className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                              disabled={item.quantity <= 1}
                            >
                              <span className="text-gray-600 font-medium">−</span>
                            </button>

                            <span className="w-8 text-center text-sm font-medium text-gray-900">
                              {item.quantity}
                            </span>

                            <button
                              onClick={() => increaseQuantity(item.id)}
                              className="w-8 h-8 flex items-center justify-center border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
                            >
                              <span className="text-gray-600 font-medium">+</span>
                            </button>

                            {/* Delete Button with spacing */}
                            <div className="ml-4">
                              <button
                                onClick={() => removeFromCart(item.id)}
                                className="w-8 h-8 flex items-center justify-center text-red-500 hover:text-red-700 hover:bg-red-50 rounded-md transition-colors"
                                title="Remove item"
                              >
                                <FaTrash className="w-3 h-3" />
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Total Column */}
                      <div className="flex items-start justify-end">
                        <span className="text-sm font-semibold text-primary">
                          {formatCurrency(item.price * item.quantity)}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </>
          )}

          {/* Cart Footer - Only show if cart has items */}
          {items.length > 0 && (
            <div className="sticky bottom-0 border-t border-gray-600 p-4 bg-white shadow-lg ">
              {/* Subtotal */}
              <div className="flex items-center justify-between mb-6 mt-4">
                <span className="text-lg font-medium text-gray-900">Subtotal:</span>
                <span className="text-lg font-bold text-primary">
                  {formatCurrency(getSubtotal ? getSubtotal() : 0)}
                </span>
              </div>

              {/* Action Buttons */}
              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="outline"
                  onClick={handleViewCart}
                  className="w-full"
                >
                  View Cart
                </Button>
                <Button
                  variant="primary"
                  onClick={handleCheckout}
                  className="w-full"
                >
                  Checkout
                </Button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Login Prompt Modal */}
      <LoginPromptModal
        isOpen={showLoginPrompt}
        onClose={() => setShowLoginPrompt(false)}
        action={loginAction}
      />
    </div>
  )
}

export default CartModal
