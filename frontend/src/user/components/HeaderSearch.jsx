import React, { useRef, useCallback, useEffect } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { FaSearch, FaTimes } from 'react-icons/fa'
import useProductStore from '../../store/productStore'

const HeaderSearch = ({ className = "" }) => {
  const navigate = useNavigate()
  const location = useLocation()
  const searchInputRef = useRef(null)

  const {
    searchQuery,
    setSearchQuery,
    clearSearch
  } = useProductStore()

  // Clear search query when navigating away from /products page
  // Use a ref to track the previous pathname to avoid clearing while typing
  const prevPathnameRef = useRef(location.pathname)

  useEffect(() => {
    // Only clear if we actually navigated away from /products (not just a re-render)
    if (prevPathnameRef.current === '/products' && location.pathname !== '/products' && searchQuery) {
      clearSearch()
    }
    prevPathnameRef.current = location.pathname
  }, [location.pathname, searchQuery, clearSearch])

  // Clear search input
  const handleClearSearch = useCallback(() => {
    clearSearch()
    searchInputRef.current?.focus()
  }, [clearSearch])

  // Handle search form submission - navigate to products from any page
  const handleSearch = useCallback((e) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      // Always navigate to products page when searching from any page
      // This ensures search results are displayed regardless of current location
      navigate('/products')
      searchInputRef.current?.blur()
    }
  }, [searchQuery, navigate])

  return (
    <div className={`relative w-full max-w-2xl mx-auto ${className}`}>
      {/* Search Form */}
      <form onSubmit={handleSearch} className="relative">
        <div className="relative group">
          <input
            ref={searchInputRef}
            type="text"
            placeholder="Search for divine products, statues, poshak..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-12 pr-12 py-3.5 border-2 border-gray-200 rounded-full focus:outline-none focus:ring-1 focus:ring-gray-400 focus:border-gray-400 transition-all duration-300 bg-white shadow-sm hover:shadow-md focus:shadow-lg text-gray-700 placeholder-gray-400"
          />

          {/* Search Icon */}
          <FaSearch className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-primary w-5 h-5 transition-colors duration-200" />

          {/* Clear Button */}
          {searchQuery && (
            <button
              type="button"
              onClick={handleClearSearch}
              className="absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors p-1 rounded-full hover:bg-gray-100"
            >
              <FaTimes className="w-4 h-4" />
            </button>
          )}
        </div>
      </form>
    </div>
  )
}

export default HeaderSearch
