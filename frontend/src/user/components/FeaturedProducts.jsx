import React from 'react'
import { Link } from 'react-router-dom'
import ProductCard from './ProductCard'
import dummyProducts from '../../data/dummyProducts'

const FeaturedProducts = () => {
  // Get first 6 products as featured products
  const featuredProducts = dummyProducts.slice(0, 6)

  return (
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-primary mb-4">
            Featured Products
          </h2>
          <p className="text-lg text-text/70 max-w-2xl mx-auto">
            Handpicked divine items that bring spirituality and beauty to your sacred space
          </p>
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
          {featuredProducts.map((product) => (
            <div 
              key={product.id}
              className="transform hover:scale-105 transition-transform duration-300"
            >
              <ProductCard product={product} />
            </div>
          ))}
        </div>

        {/* View All Products Button */}
        <div className="text-center">
          <Link 
            to="/products"
            className="inline-flex items-center px-8 py-4 bg-gradient-to-r from-primary to-red-600 text-white font-semibold rounded-full hover:from-red-600 hover:to-red-700 transform hover:scale-105 transition-all duration-300 shadow-lg hover:shadow-xl"
          >
            View All Products
            <svg className="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
            </svg>
          </Link>
        </div>
      </div>
    </section>
  )
}

export default FeaturedProducts
