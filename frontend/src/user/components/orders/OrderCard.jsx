import React, { useState } from 'react'
import { FaChevronDown, FaChevronUp } from 'react-icons/fa'
import OrderItemCard from './OrderItemCard'

const OrderCard = ({ order, formatCurrency, formatDate, getTotalItems }) => {
  const [isExpanded, setIsExpanded] = useState(false)

  const toggleDetails = () => {
    setIsExpanded(!isExpanded)
  }

  return (
    <div className="border border-gray-200 rounded-lg overflow-hidden hover:shadow-md transition-shadow">
      {/* Order Header with Custom Background */}
      <div className="bg-gray-100 px-4 sm:px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-black">Order {order.id}</h3>
          </div>
          <div>
            <p className="text-gray-600 text-sm sm:text-base">
              Placed on {formatDate(order.date)}
            </p>
          </div>
        </div>
      </div>

      {/* Order Summary */}
      <div className="px-4 sm:px-6 py-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          {/* Summary Info */}
          <div className="flex flex-col sm:flex-row sm:items-center sm:space-x-8 space-y-2 sm:space-y-0">
            <div>
              <p className="text-sm text-gray-500 uppercase tracking-wide">Total Items</p>
              <p className="text-lg font-medium text-black">
                {getTotalItems(order.items)} item{getTotalItems(order.items) !== 1 ? 's' : ''}
              </p>
            </div>
            <div>
              <p className="text-sm text-gray-500 uppercase tracking-wide">Total Amount</p>
              <p className="text-lg font-semibold text-black">
                {formatCurrency(order.totalAmount)}
              </p>
            </div>
          </div>

          {/* View Details Button */}
          <button
            onClick={toggleDetails}
            className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2"
            aria-expanded={isExpanded}
            aria-controls={`order-details-${order.id}`}
          >
            <span className="text-sm font-medium text-gray-700">
              {isExpanded ? 'Hide Details' : 'View Details'}
            </span>
            {isExpanded ? (
              <FaChevronUp className="w-4 h-4 text-gray-500" />
            ) : (
              <FaChevronDown className="w-4 h-4 text-gray-500" />
            )}
          </button>
        </div>
      </div>

      {/* Expandable Order Details */}
      {isExpanded && (
        <div
          id={`order-details-${order.id}`}
          className="border-t border-gray-200 bg-gray-50 px-4 sm:px-6 py-4"
        >
          <div className="space-y-4">
            {order.items.map((item) => (
              <OrderItemCard
                key={item.id}
                item={item}
                formatCurrency={formatCurrency}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default OrderCard
