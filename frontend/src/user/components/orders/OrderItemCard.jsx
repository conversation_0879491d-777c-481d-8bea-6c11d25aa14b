import React from 'react'

const OrderItemCard = ({ item, formatCurrency }) => {
  return (
    <div className="flex items-center space-x-4 p-4 bg-white rounded-lg border border-gray-100">
      {/* Product Thumbnail */}
      <div className="flex-shrink-0">
        <img
          src={item.thumbnail}
          alt={item.title}
          className="w-16 h-16 sm:w-20 sm:h-20 object-cover rounded-lg border border-gray-200"
          loading="lazy"
        />
      </div>

      {/* Product Details */}
      <div className="flex-1 min-w-0">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
          {/* Product Info */}
          <div className="flex-1 min-w-0 mb-2 sm:mb-0">
            <h5 className="text-sm sm:text-base font-medium text-gray-900 truncate">
              {item.title}
            </h5>
            <div className="flex items-center space-x-4 mt-1">
              <p className="text-sm text-gray-500">
                Qty: <span className="font-medium text-gray-700">{item.quantity}</span>
              </p>
              <p className="text-sm text-gray-500">
                Price: <span className="font-medium text-gray-700">{formatCurrency(item.price)}</span>
              </p>
            </div>
          </div>

          {/* Total Price */}
          <div className="flex-shrink-0">
            <p className="text-sm sm:text-base font-semibold text-gray-900">
              {formatCurrency(item.price * item.quantity)}
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default OrderItemCard
