import React from 'react'
import useCartStore from "../../store/cartStore";

const CartItem = ({item}) => {
    
    const increaseQuantity = useCartStore((state) => state.increaseQuantity);
    const decreaseQuantity = useCartStore((state) => state.decreaseQuantity);
    const removeFromCart = useCartStore((state) => state.removeFromCart);

  return (
    <div className="border-b py-4">
      <div className="flex flex-row md:flex-row items-start gap-4 py-4">
        {/* Image */}
        <div className="w-24 h-24 flex-shrink-0">
          <img
            src={item.image}
            alt={item.name}
            className="w-full h-full object-cover rounded border"
          />
        </div>

        {/* Right Side Content */}
        <div className="flex-1 flex flex-col justify-between">
            {/* Name & Price */}
            <div>
                <h2 className="text-base font-semibold">{item.name}</h2>
                <p className="text-sm text-gray-600 mt-1">₹{(item.price).toFixed(2)}</p>
            </div>

          {/* Quantity Controls & Delete */}
            <div className="mt-3 flex items-center gap-4 flex-wrap">
                <div className="flex items-center gap-2 border px-2 py-1 rounded">
                    <button 
                        onClick={() => decreaseQuantity(item.id)} 
                        className="text-xl font-bold"
                    > 
                        −
                    </button>
                    <span>{item.quantity}</span>
                    <button 
                        onClick={() => increaseQuantity(item.id)} 
                        className="text-xl font-bold"
                    >
                        + 
                    </button>
                </div>
                    <button
                        onClick={() => removeFromCart(item.id)}
                        className="text-primary text-sm hover:underline border-l-2 pl-2"
                    >
                    Delete
                    </button>
            </div>

            {/* Subtotal on Mobile */}
            <div className="mt-3 text-sm font-semibold text-gray-700 md:hidden border-t pt-2">
                SubTotal: ₹{(item.price * item.quantity).toFixed(2)}
            </div>
        </div>

        {/* Subtotal on Desktop */}
        <div className="hidden md:block md:w-32 text-lg font-bold text-right">
            ₹{(item.price * item.quantity).toFixed(2)}
        </div>
      </div>
    </div>
  )
}

export default CartItem
