import React from 'react'
import { Link } from 'react-router-dom'
import logo from '../../../assets/Logo1.png'

const CheckoutHeader = () => {
  return (
    <div className="top-0 z-50">
      {/* Header Section with Logo */}
      <header className="bg-background border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-center items-center m-6 h-16 sm:h-20">
            {/* Logo */}
            <Link
              to="/"
              className="flex items-center hover:opacity-80 transition-opacity duration-200"
              aria-label="ShringarHub - Go to homepage"
            >
              <img
                src={logo}
                alt="ShringarHub"
                className="w-16 h-16 sm:w-20 sm:h-20 object-contain rounded-full"
              />
            </Link>
          </div>
        </div>
      </header>

      {/* Navigation Section for Visual Separation */}
      <nav className="bg-primary border-b border-red-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="h-12 sm:h-8">
            {/* Empty nav section for visual separation */}
          </div>
        </div>
      </nav>
    </div>
  )
}

export default CheckoutHeader
