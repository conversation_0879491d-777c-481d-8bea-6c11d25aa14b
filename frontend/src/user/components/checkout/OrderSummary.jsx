import React, { useState } from 'react'
import { FaShoppingBag, FaChevronDown, FaChevronUp } from 'react-icons/fa'
import useCartStore from '../../../store/cartStore'
import SummaryItem from './SummaryItem'
import Button from '../../../shared/ui/Button'

const OrderSummary = ({ onPlaceOrder, isSubmitting }) => {
  const { cartItems, getSubtotal, getAppliedDiscount, getFinalTotal, getTotalQuantity, appliedCoupon } = useCartStore()
  const [isExpanded, setIsExpanded] = useState(false) // Collapsed by default on mobile

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  const subtotal = getSubtotal()
  const discount = getAppliedDiscount()
  const shippingCost = subtotal >= 500 ? 0 : 50
  const finalTotal = getFinalTotal() + shippingCost

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Header */}
      <div
        className="p-6 border-b border-gray-100 lg:cursor-default cursor-pointer lg:hover:bg-transparent hover:bg-gray-50 transition-colors"
        onClick={() => window.innerWidth < 1024 && toggleExpanded()}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <FaShoppingBag className="w-5 h-5 text-gray-600" />
            <h2 className="text-lg font-semibold text-gray-900">
              Order Summary
            </h2>
            <span className="text-sm text-gray-500">
              ({getTotalQuantity()} {getTotalQuantity() === 1 ? 'item' : 'items'})
            </span>
          </div>
          {/* Show/Hide toggle only on mobile */}
          <div className="lg:hidden">
            {isExpanded ? (
              <FaChevronUp className="w-4 h-4 text-gray-400" />
            ) : (
              <FaChevronDown className="w-4 h-4 text-gray-400" />
            )}
          </div>
        </div>
      </div>

      {/* Collapsible Content - Always visible on desktop, toggleable on mobile */}
      <div className={`lg:block ${isExpanded ? 'block' : 'hidden'}`}>
        {/* Items List */}
        <div className="p-6 max-h-80 overflow-y-auto">
          {cartItems.map((item) => (
            <SummaryItem key={item.id} item={item} />
          ))}
        </div>

        {/* Order Totals */}
        <div className="p-6 border-t border-gray-100 space-y-3">
        {/* Subtotal */}
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Subtotal</span>
          <span className="font-medium">{formatCurrency(subtotal)}</span>
        </div>

        {/* Discount */}
        {discount > 0 && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">
              Discount {appliedCoupon && `(${appliedCoupon})`}
            </span>
            <span className="font-medium text-green-600">
              -{formatCurrency(discount)}
            </span>
          </div>
        )}

        {/* Shipping */}
        <div className="flex justify-between text-sm">
          <span className="text-gray-600">Shipping</span>
          <span className="font-medium">
            {shippingCost === 0 ? (
              <span className="text-green-600">Free</span>
            ) : (
              formatCurrency(shippingCost)
            )}
          </span>
        </div>

        {/* Free Shipping Notice */}
        {subtotal < 500 && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
            <p className="text-blue-800 text-xs">
              Add {formatCurrency(500 - subtotal)} more for free shipping!
            </p>
          </div>
        )}

        {/* Total */}
        <div className="border-t border-gray-200 pt-3">
          <div className="flex justify-between">
            <span className="text-base font-semibold text-gray-900">Total</span>
            <span className="text-lg font-bold text-primary">
              {formatCurrency(finalTotal)}
            </span>
          </div>
        </div>
      </div>

        {/* Place Order Button */}
        <div className="p-6 border-t border-gray-100">
        <Button
          onClick={onPlaceOrder}
          disabled={isSubmitting}
          className="w-full py-3 text-base font-semibold"
          variant="primary"
        >
          {isSubmitting ? 'Processing...' : `Place Order • ${formatCurrency(finalTotal)}`}
        </Button>
        
          {/* Security Notice */}
          <div className="mt-3 text-center">
            <p className="text-xs text-gray-500">
              🔒 Your payment information is secure and encrypted
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default OrderSummary
