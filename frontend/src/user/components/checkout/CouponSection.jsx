import React, { useState, useEffect } from 'react'
import { FaTag, FaTimes } from 'react-icons/fa'
import { toast } from 'sonner'
import useCouponStore from '../../../store/couponStore'
import useCartStore from '../../../store/cartStore'
import Button from '../../../shared/ui/Button'

const CouponSection = ({ subtotal }) => {
  const {
    couponCode,
    appliedCoupon,
    discountAmount,
    isApplying,
    error,
    setCouponCode,
    applyCoupon,
    clearCoupon,
    clearError
  } = useCouponStore()

  const { cartItems } = useCartStore()
  const [localCouponCode, setLocalCouponCode] = useState('')

  // Clear coupon when cart becomes empty
  useEffect(() => {
    if (cartItems.length === 0 && appliedCoupon) {
      clearCoupon()
      toast.info('Coupon removed - cart is empty')
    }
  }, [cartItems.length, appliedCoupon, clearCoupon])

  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  const handleInputChange = (e) => {
    const value = e.target.value.toUpperCase()
    setLocalCouponCode(value)
    setCouponCode(value)
    if (error) clearError()
  }

  const handleApplyCoupon = async () => {
    if (!localCouponCode.trim()) {
      toast.error('Please enter a coupon code')
      return
    }

    const success = await applyCoupon(localCouponCode, subtotal)
    if (success) {
      setLocalCouponCode('')
      toast.success(`Coupon applied! You saved ${formatCurrency(discountAmount)}`)
    } else {
      toast.error(error || 'Failed to apply coupon')
    }
  }

  const handleRemoveCoupon = () => {
    clearCoupon()
    setLocalCouponCode('')
    toast.success('Coupon removed')
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleApplyCoupon()
    }
  }

  return (
    <div className="border-t border-gray-200 pt-4">
      <div className="flex items-center mb-3">
        <FaTag className="w-4 h-4 text-gray-500 mr-2" />
        <h3 className="text-sm font-medium text-gray-900">Promo Code</h3>
      </div>

      {appliedCoupon ? (
        /* Applied Coupon Display */
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-background border border-gray-200 rounded-lg">
            <div className="flex items-center">
              <FaTag className="w-4 h-4 text-gray-500 mr-2" />
              <div>
                <span className="text-sm font-medium text-gray-900">
                  {appliedCoupon.code}
                </span>
                <p className="text-xs text-gray-900">
                  {appliedCoupon.description} applied
                </p>
              </div>
            </div>
            <button
              onClick={handleRemoveCoupon}
              className="p-1 text-gary-700 hover:text-gray-900 transition-colors"
              title="Remove coupon"
            >
              <FaTimes className="w-3 h-3" />
            </button>
          </div>

          {/* Discount Amount Display */}
          <div className="flex justify-between text-sm">
            <span className="text-gray-600">
              Discount ({appliedCoupon.code})
            </span>
            <span className="font-medium text-gray-900">
              -{formatCurrency(discountAmount)}
            </span>
          </div>
        </div>
      ) : (
        /* Coupon Input */
        <div className="space-y-3">
          <div className="flex gap-2">
            <input
              type="text"
              placeholder="Enter coupon code"
              value={localCouponCode}
              onChange={handleInputChange}
              onKeyPress={handleKeyPress}
              className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary focus:border-transparent"
              disabled={isApplying}
            />
            <Button
              onClick={handleApplyCoupon}
              disabled={!localCouponCode.trim() || isApplying}
              className="px-4 py-2 text-sm"
              variant="outline"
            >
              {isApplying ? 'Applying...' : 'Apply'}
            </Button>
          </div>

          {/* Error Message */}
          {error && (
            <p className="text-red-600 text-xs">{error}</p>
          )}

          {/* Available Coupons Hint */}
          <div className="text-xs text-gray-500">
            <p>Try: SAVE10, FLAT50, WELCOME15, NEWUSER, FESTIVAL20</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default CouponSection
