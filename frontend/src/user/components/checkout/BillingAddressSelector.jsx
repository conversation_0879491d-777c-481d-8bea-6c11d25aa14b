import React, { useState, useEffect, useRef } from 'react'
import { FaCreditCard, FaChevronDown, FaChevronUp, FaPlus, FaEllipsisV, FaEdit, FaTrash } from 'react-icons/fa'
import { toast } from 'sonner'
import useCheckoutStore from '../../../store/checkoutStore'
import useAddressStore from '../../../store/addressStore'
import AddressFormModal from '../account/AddressFormModal'
import ConfirmModal from '../../../shared/ui/ConfirmModal'

const BillingAddressSelector = () => {
  const { useSameAddress, setUseSameAddress, setBillingAddress } = useCheckoutStore()
  const {
    billingAddresses,
    addBillingAddress,
    updateBillingAddress,
    deleteBillingAddress,
    getSelectedBillingAddress,
    selectBillingAddress
  } = useAddressStore()
  const [isExpanded, setIsExpanded] = useState(true)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingAddress, setEditingAddress] = useState(null)
  const [showDropdown, setShowDropdown] = useState(false)
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const [addressToDelete, setAddressToDelete] = useState(null)
  const dropdownRef = useRef(null)

  // Get selected billing address from store
  const selectedBillingAddress = getSelectedBillingAddress()

  // Sync selected billing address with checkout store
  useEffect(() => {
    if (!useSameAddress && selectedBillingAddress) {
      const billingData = convertToBillingFormat(selectedBillingAddress)
      setBillingAddress(billingData)
    }
  }, [selectedBillingAddress, useSameAddress, setBillingAddress])

  // Debounced dropdown close on outside click
  useEffect(() => {
    let timeoutId = null

    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        // Add small debounce to prevent flickers during interactions
        timeoutId = setTimeout(() => {
          setShowDropdown(false)
        }, 100)
      }
    }

    const handleFocusIn = (event) => {
      // Cancel timeout if focus moves to dropdown
      if (dropdownRef.current && dropdownRef.current.contains(event.target)) {
        if (timeoutId) {
          clearTimeout(timeoutId)
          timeoutId = null
        }
      }
    }

    if (showDropdown) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('focusin', handleFocusIn)
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('focusin', handleFocusIn)
    }
  }, [showDropdown])

  const handleAddressOptionChange = (useSame) => {
    setUseSameAddress(useSame)
    if (useSame) {
      // Clear billing address when using same as shipping
      setBillingAddress({})
      selectBillingAddress(null)
    } else if (!selectedBillingAddress) {
      // If switching to different address but no address selected, clear billing data
      setBillingAddress({})
    }
  }

  // Convert address store format to checkout billing format
  const convertToBillingFormat = (address) => {
    if (!address) return {}

    let firstName = '', lastName = ''
    if (address.firstName && address.lastName) {
      firstName = address.firstName
      lastName = address.lastName
    } else if (address.fullName) {
      [firstName = '', lastName = ''] = address.fullName.split(' ')
    }

    return {
      firstName,
      lastName,
      email: '', // Will be filled from user data
      phone: address.phoneNumber || '',
      address: address.addressLine1 || '',
      apartment: address.addressLine2 || '',
      city: address.city || '',
      state: address.state || '',
      zipCode: address.zipCode || '',
      country: 'India'
    }
  }

  // Check if billing form has data (when different address is selected)
  const hasCustomBillingData = !useSameAddress && selectedBillingAddress

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  // Modal handlers
  const openAddModal = () => {
    setEditingAddress(null)
    setIsModalOpen(true)
  }

  const openEditModal = () => {
    setEditingAddress(selectedBillingAddress)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    setEditingAddress(null)
  }

  // Handle billing address selection
  const handleSelectBillingAddress = (address) => {
    selectBillingAddress(address.id)
    const billingData = convertToBillingFormat(address)
    setBillingAddress(billingData)
  }

  // Dropdown handlers
  const toggleDropdown = () => {
    setShowDropdown(!showDropdown)
  }

  // Show delete confirmation modal
  const handleDeleteClick = () => {
    if (selectedBillingAddress) {
      setAddressToDelete(selectedBillingAddress)
      setShowConfirmModal(true)
      setShowDropdown(false)
    }
  }

  // Confirm delete billing address
  const handleConfirmDelete = () => {
    if (addressToDelete) {
      try {
        deleteBillingAddress(addressToDelete.id)

        // Clear billing selection and data
        selectBillingAddress(null)
        setBillingAddress({})

        // Show success toast
        toast.success('Billing address deleted successfully!')

        // Reset delete state
        setAddressToDelete(null)
      } catch (error) {
        console.error('Error deleting billing address:', error)
        toast.error('Failed to delete billing address. Please try again.')
      }
    }
  }

  // Cancel delete
  const handleCancelDelete = () => {
    setShowConfirmModal(false)
    setAddressToDelete(null)
  }

  const handleAddAddress = (addressData) => {
    try {
      // Add to billing address store
      addBillingAddress(addressData)

      // Convert and set as billing address in checkout store
      const billingData = convertToBillingFormat(addressData)
      setBillingAddress(billingData)

      // Show success toast
      toast.success('Billing address saved successfully!')

      setIsModalOpen(false)
    } catch (error) {
      console.error('Error saving billing address:', error)
      toast.error('Failed to save billing address. Please try again.')
    }
  }

  const handleEditAddress = (addressData) => {
    try {
      // Update in billing address store
      updateBillingAddress(editingAddress.id, addressData)

      // Convert and set as billing address in checkout store
      const billingData = convertToBillingFormat(addressData)
      setBillingAddress(billingData)

      // Show success toast
      toast.success('Billing address updated successfully!')

      setEditingAddress(null)
      setIsModalOpen(false)
    } catch (error) {
      console.error('Error updating billing address:', error)
      toast.error('Failed to update billing address. Please try again.')
    }
  }



  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Header */}
      <div
        className="flex items-center justify-between p-6 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={toggleExpanded}
      >
        <div className="flex items-center space-x-2">
          <FaCreditCard className="w-5 h-5 text-gray-600" />
          <h2 className="text-lg font-semibold text-gray-900">Billing Address</h2>
          {(useSameAddress || hasCustomBillingData) && !isExpanded && (
            <span className="text-sm text-green-600 font-medium">✓ Completed</span>
          )}
        </div>
        <div className="flex items-center space-x-2">
          {!isExpanded && (
            <span className="text-sm text-gray-600">
              {useSameAddress ? 'Same as shipping' : 'Different address'}
            </span>
          )}
          {isExpanded ? (
            <FaChevronUp className="w-4 h-4 text-gray-400" />
          ) : (
            <FaChevronDown className="w-4 h-4 text-gray-400" />
          )}
        </div>
      </div>

      {/* Collapsible Content */}
      {isExpanded && (
        <div className="px-6 pb-6 border-t border-gray-100">
          {/* Radio Options */}
          <div className="space-y-4 mt-6">
        {/* Same as Shipping */}
        <label className="flex items-start space-x-3 cursor-pointer">
          <input
            type="radio"
            name="billingAddress"
            checked={useSameAddress}
            onChange={() => handleAddressOptionChange(true)}
            className="mt-1 h-4 w-4 text-primary border-gray-300 focus:ring-primary"
          />
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-900">
              Same as shipping address
            </div>
            <div className="text-sm text-gray-600">
              Use the same address for billing and shipping
            </div>
          </div>
        </label>

        {/* Different Billing Address */}
        <label className="flex items-start space-x-3 cursor-pointer">
          <input
            type="radio"
            name="billingAddress"
            checked={!useSameAddress}
            onChange={() => handleAddressOptionChange(false)}
            className="mt-1 h-4 w-4 text-primary border-gray-300 focus:ring-primary"
          />
          <div className="flex-1">
            <div className="text-sm font-medium text-gray-900">
              Use a different billing address
            </div>
            <div className="text-sm text-gray-600">
              Provide a separate address for billing
            </div>
          </div>
        </label>
      </div>

          {/* Conditional Billing Address Display */}
          {!useSameAddress && (
            <div className="mt-6 pt-4 border-t border-gray-200">
              {selectedBillingAddress ? (
                /* Display Selected Billing Address */
                <div>
                  <h5 className="text-sm font-medium text-gray-900 mb-4">
                    Billing address:
                  </h5>

                  <div className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h6 className="font-medium text-gray-900">
                          {selectedBillingAddress.firstName && selectedBillingAddress.lastName
                            ? `${selectedBillingAddress.firstName} ${selectedBillingAddress.lastName}`
                            : selectedBillingAddress.fullName || 'Unknown Name'
                          }
                        </h6>
                        <p className="text-sm text-gray-600 mt-1">
                          {selectedBillingAddress.addressLine1}
                          {selectedBillingAddress.addressLine2 && `, ${selectedBillingAddress.addressLine2}`}
                        </p>
                        <p className="text-sm text-gray-600">
                          {selectedBillingAddress.city}, {selectedBillingAddress.state} {selectedBillingAddress.zipCode}
                        </p>
                        <p className="text-sm text-gray-600">
                          +91 {selectedBillingAddress.phoneNumber}
                        </p>
                      </div>

                      {/* Three Dot Menu */}
                      <div className="relative ml-4" ref={dropdownRef}>
                        <button
                          onClick={toggleDropdown}
                          className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                          title="More options"
                        >
                          <FaEllipsisV className="w-4 h-4" />
                        </button>

                        {/* Dropdown Menu */}
                        {showDropdown && (
                          <div className="absolute right-0 top-full mt-1 w-40 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                            <button
                              onClick={() => {
                                openEditModal()
                                setShowDropdown(false)
                              }}
                              className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                            >
                              <FaEdit className="w-3 h-3" />
                              <span>Edit</span>
                            </button>
                            <button
                              onClick={handleDeleteClick}
                              className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                            >
                              <FaTrash className="w-3 h-3" />
                              <span>Delete</span>
                            </button>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                /* No Billing Address - Show Add Button */
                <div className="text-center py-6">
                  <button
                    onClick={openAddModal}
                    className="inline-flex items-center space-x-2 bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark transition-colors"
                  >
                    <FaPlus className="w-4 h-4" />
                    <span>Add Billing Address</span>
                  </button>
                </div>
              )}
            </div>
          )}
        </div>
      )}

      {/* Address Form Modal */}
      <AddressFormModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onSave={editingAddress ? handleEditAddress : handleAddAddress}
        initialData={editingAddress}
        title={editingAddress ? 'Edit Billing Address' : 'Add Billing Address'}
        mode="billing"
      />

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={showConfirmModal}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Delete Billing Address"
        message="Are you sure you want to delete this billing address? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        confirmVariant="danger"
        icon="danger"
      />
    </div>
  )
}

export default BillingAddressSelector
 