import React, { useState, useEffect, useRef } from 'react'
import { FaTruck, FaPlus, FaEdit, FaChevronDown, FaChevronUp, FaEllipsisV, FaTrash } from 'react-icons/fa'
import { toast } from 'sonner'
import useAddressStore from '../../../store/addressStore'
import useCheckoutStore from '../../../store/checkoutStore'
import AddressFormModal from '../account/AddressFormModal'
import ConfirmModal from '../../../shared/ui/ConfirmModal'

const AddressSelector = () => {
  const { addresses, getDefaultAddress, addAddress, updateAddress, deleteAddress } = useAddressStore()
  const { setShippingAddress } = useCheckoutStore()

  const [isExpanded, setIsExpanded] = useState(true)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingAddress, setEditingAddress] = useState(null)
  const [selectedAddress, setSelectedAddress] = useState(null)
  const [showDropdown, setShowDropdown] = useState(false)
  const [showConfirmModal, setShowConfirmModal] = useState(false)
  const [addressToDelete, setAddressToDelete] = useState(null)
  const dropdownRef = useRef(null)

  // Initialize with default address on component mount
  useEffect(() => {
    const defaultAddress = getDefaultAddress()
    if (defaultAddress) {
      setSelectedAddress(defaultAddress)
      // Convert address format to checkout format
      const checkoutAddress = convertToCheckoutFormat(defaultAddress)
      setShippingAddress(checkoutAddress)
    }
  }, [addresses, getDefaultAddress, setShippingAddress])

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Convert address store format to checkout store format
  const convertToCheckoutFormat = (address) => {
    if (!address) return {}

    // Handle both new format (firstName, lastName) and old format (fullName)
    let firstName = '', lastName = ''

    if (address.firstName && address.lastName) {
      firstName = address.firstName
      lastName = address.lastName
    } else if (address.fullName) {
      [firstName = '', lastName = ''] = address.fullName.split(' ')
    }

    return {
      firstName,
      lastName,
      email: '', // Will be filled from user data
      phone: address.phoneNumber || '',
      address: address.addressLine1 || '',
      apartment: address.addressLine2 || '',
      city: address.city || '',
      state: address.state || '',
      zipCode: address.zipCode || '',
      country: 'India'
    }
  }

  // Convert checkout format to address store format
  const convertToAddressFormat = (checkoutAddress, isDefault = false) => {
    return {
      fullName: `${checkoutAddress.firstName || ''} ${checkoutAddress.lastName || ''}`.trim(),
      addressLine1: checkoutAddress.address || '',
      addressLine2: checkoutAddress.apartment || '',
      city: checkoutAddress.city || '',
      state: checkoutAddress.state || '',
      zipCode: checkoutAddress.zipCode || '',
      phoneNumber: checkoutAddress.phone || '',
      isDefault
    }
  }

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  const handleAddAddress = (addressData) => {
    // Add to address store
    addAddress(addressData)
    
    // Convert and set as shipping address
    const checkoutAddress = convertToCheckoutFormat(addressData)
    setShippingAddress(checkoutAddress)
    
    // Update selected address
    setSelectedAddress({ ...addressData, id: Date.now().toString() })
    
    setIsModalOpen(false)
  }

  const handleEditAddress = (addressData) => {
    // Update in address store
    updateAddress(editingAddress.id, addressData)
    
    // Convert and set as shipping address
    const checkoutAddress = convertToCheckoutFormat(addressData)
    setShippingAddress(checkoutAddress)
    
    // Update selected address
    setSelectedAddress({ ...addressData, id: editingAddress.id })
    
    setEditingAddress(null)
    setIsModalOpen(false)
  }

  const openAddModal = () => {
    setEditingAddress(null)
    setIsModalOpen(true)
  }

  const openEditModal = () => {
    setEditingAddress(selectedAddress)
    setIsModalOpen(true)
  }

  const closeModal = () => {
    setIsModalOpen(false)
    setEditingAddress(null)
  }

  const handleSelectAddress = (address) => {
    setSelectedAddress(address)
    const checkoutAddress = convertToCheckoutFormat(address)
    setShippingAddress(checkoutAddress)
  }

  // Show delete confirmation modal
  const handleDeleteClick = () => {
    if (selectedAddress) {
      setAddressToDelete(selectedAddress)
      setShowConfirmModal(true)
      setShowDropdown(false)
    }
  }

  // Confirm delete shipping address
  const handleConfirmDelete = () => {
    if (addressToDelete) {
      try {
        deleteAddress(addressToDelete.id)

        // Find next available address or clear selection
        const remainingAddresses = addresses.filter(addr => addr.id !== addressToDelete.id)
        if (remainingAddresses.length > 0) {
          const nextAddress = remainingAddresses.find(addr => addr.isDefault) || remainingAddresses[0]
          setSelectedAddress(nextAddress)
          const checkoutAddress = convertToCheckoutFormat(nextAddress)
          setShippingAddress(checkoutAddress)
        } else {
          setSelectedAddress(null)
          setShippingAddress({})
        }

        // Show success toast
        toast.success('Shipping address deleted successfully!')

        // Reset delete state
        setAddressToDelete(null)
      } catch (error) {
        console.error('Error deleting shipping address:', error)
        toast.error('Failed to delete shipping address. Please try again.')
      }
    }
  }

  // Cancel delete
  const handleCancelDelete = () => {
    setShowConfirmModal(false)
    setAddressToDelete(null)
  }

  const toggleDropdown = () => {
    setShowDropdown(!showDropdown)
  }



  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Header */}
      <div 
        className="flex items-center justify-between p-6 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={toggleExpanded}
      >
        <div className="flex items-center space-x-2">
          <FaTruck className="w-5 h-5 text-gray-600" />
          <h2 className="text-lg font-semibold text-gray-900">Shipping Address</h2>
          {/* {selectedAddress && !isExpanded && (
            <span className="text-sm text-green-600 font-medium">✓ Selected</span>
          )} */}
        </div>
        <div className="flex items-center space-x-2">
          {isExpanded ? (
            <FaChevronUp className="w-4 h-4 text-gray-400" />
          ) : (
            <FaChevronDown className="w-4 h-4 text-gray-400" />
          )}
        </div>
      </div>

      {/* Selected Address Preview (when collapsed) */}
      {!isExpanded && selectedAddress && (
        <div className="px-6 pb-4 border-t border-gray-200">
          <div className="mt-2 p-3  rounded-lg ">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <h4 className="font-medium text-gray-900 text-sm">
                  {selectedAddress.firstName && selectedAddress.lastName
                    ? `${selectedAddress.firstName} ${selectedAddress.lastName}`
                    : selectedAddress.fullName || 'Unknown Name'
                  }
                  {selectedAddress.isDefault && (
                    <span className="ml-2 inline-block px-2 py-0.5 bg-primary/10 text-primary text-xs font-medium rounded">
                      Default
                    </span>
                  )}
                </h4>
                <p className="text-xs text-gray-600 mt-1">
                  {selectedAddress.addressLine1}
                  {selectedAddress.addressLine2 && `, ${selectedAddress.addressLine2}`}
                </p>
                <p className="text-xs text-gray-600">
                  {selectedAddress.city}, {selectedAddress.state} {selectedAddress.zipCode}
                </p>
              </div>
              <span className="text-sm text-green-600 font-medium">✓ Selected</span>
            </div>
          </div>
        </div>
      )}

      {/* Collapsible Content */}
      {isExpanded && (
        <div className="px-6 pb-6 border-t border-gray-100">
          {addresses.length > 0 ? (
            /* Saved Addresses as Radio Buttons */
            <div className="mt-6">
              <h4 className="text-sm font-medium text-gray-900 mb-4">
                Choose shipping address:
              </h4>

              {/* Radio Button List */}
              <div className="space-y-3">
                {addresses.map((address) => (
                  <label
                    key={address.id}
                    className={`
                      relative flex items-start p-4 border rounded-lg cursor-pointer transition-all
                      ${selectedAddress?.id === address.id
                        ? 'border-primary bg-primary/5 ring-2 ring-primary/20'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }
                    `}
                  >
                    <input
                      type="radio"
                      name="shippingAddress"
                      value={address.id}
                      checked={selectedAddress?.id === address.id}
                      onChange={() => handleSelectAddress(address)}
                      className="mt-1 h-4 w-4 text-primary border-gray-300 focus:ring-primary"
                    />

                    <div className="ml-3 flex-1">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h5 className="font-medium text-gray-900">
                            {address.firstName && address.lastName
                              ? `${address.firstName} ${address.lastName}`
                              : address.fullName || 'Unknown Name'
                            }
                            {address.isDefault && (
                              <span className="ml-2 inline-block px-2 py-0.5 bg-primary/10 text-primary text-xs font-medium rounded">
                                Default
                              </span>
                            )}
                          </h5>
                          <p className="text-sm text-gray-600 mt-1">
                            {address.addressLine1}
                            {address.addressLine2 && `, ${address.addressLine2}`}
                          </p>
                          <p className="text-sm text-gray-600">
                            {address.city}, {address.state} {address.zipCode}
                          </p>
                          <p className="text-sm text-gray-600">
                            +91 {address.phoneNumber}
                          </p>
                        </div>

                        {/* Three Dot Menu - Only show for selected address */}
                        {selectedAddress?.id === address.id && (
                          <div className="relative ml-4" ref={dropdownRef}>
                            <button
                              onClick={(e) => {
                                e.preventDefault()
                                e.stopPropagation()
                                toggleDropdown()
                              }}
                              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                              title="More options"
                            >
                              <FaEllipsisV className="w-4 h-4" />
                            </button>

                            {/* Dropdown Menu */}
                            {showDropdown && (
                              <div className="absolute right-0 top-full mt-1 w-40 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                                <button
                                  onClick={(e) => {
                                    e.preventDefault()
                                    e.stopPropagation()
                                    openEditModal()
                                    setShowDropdown(false)
                                  }}
                                  className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                                >
                                  <FaEdit className="w-3 h-3" />
                                  <span>Edit</span>
                                </button>
                                <button
                                  onClick={(e) => {
                                    e.preventDefault()
                                    e.stopPropagation()
                                    handleDeleteClick()
                                  }}
                                  className="w-full flex items-center space-x-2 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                                >
                                  <FaTrash className="w-3 h-3" />
                                  <span>Delete</span>
                                </button>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </label>
                ))}
              </div>

              {/* Add New Address Button */}
              <div className="mt-6 pt-4 border-t border-gray-200">
                <button
                  onClick={openAddModal}
                  className="flex items-center space-x-2 text-primary hover:text-primary-dark font-medium transition-colors"
                >
                  <FaPlus className="w-4 h-4" />
                  <span>Add a new address</span>
                </button>
              </div>
            </div>
          ) : (
            /* No Address State */
            <div className="mt-6 text-center py-8">
              <FaTruck className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <h4 className="text-lg font-medium text-gray-900 mb-2">
                No shipping address selected
              </h4>
              <p className="text-gray-600 mb-6">
                Add an address to continue with your order
              </p>
              <button
                onClick={openAddModal}
                className="inline-flex items-center space-x-2 bg-primary text-white px-6 py-2 rounded-lg hover:bg-primary-dark transition-colors"
              >
                <FaPlus className="w-4 h-4" />
                <span>Add Address</span>
              </button>
            </div>
          )}
        </div>
      )}

      {/* Address Form Modal */}
      <AddressFormModal
        isOpen={isModalOpen}
        onClose={closeModal}
        onSave={editingAddress ? handleEditAddress : handleAddAddress}
        initialData={editingAddress}
        title={editingAddress ? 'Edit Shipping Address' : 'Add Shipping Address'}
      />

      {/* Delete Confirmation Modal */}
      <ConfirmModal
        isOpen={showConfirmModal}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title="Delete Shipping Address"
        message="Are you sure you want to delete this shipping address? This action cannot be undone."
        confirmText="Delete"
        cancelText="Cancel"
        confirmVariant="danger"
        icon="danger"
      />
    </div>
  )
}

export default AddressSelector
