import React, { useState } from 'react'
import {
  FaWallet,
  FaChevronDown,
  FaChevronUp,
  FaShieldAlt,
  FaCreditCard,
  FaMobile,
  FaUniversity,
  FaCheckCircle
} from 'react-icons/fa'
import useCheckoutStore from '../../../store/checkoutStore'

// NOTE: This is a frontend-only Razorpay integration for demo purposes
// TODO: When backend is ready, implement proper order creation and payment verification
// TODO: Add server-side payment verification for security
// TODO: Store order details in database after successful payment

const PaymentOptions = () => {
  const { paymentMethod, setPaymentMethod, errors } = useCheckoutStore()
  const [isExpanded, setIsExpanded] = useState(true)
  const [selectedSubMethod, setSelectedSubMethod] = useState('upi')

  const paymentOptions = [
    {
      id: 'razorpay',
      name: 'Online Payment',
      description: 'Pay securely using multiple payment methods',
      icon: FaWallet,
      recommended: true,
      subMethods: [
        {
          id: 'upi',
          name: 'UPI',
          description: 'Pay using any UPI app',
          icon: FaMobile,
          logos: ['gpay', 'phonepe', 'paytm', 'upi']
        },
        {
          id: 'cards',
          name: 'Credit/Debit Cards',
          description: 'Visa, Mastercard, RuPay',
          icon: FaCreditCard,
          logos: ['visa', 'mastercard', 'rupay']
        },
        {
          id: 'netbanking',
          name: 'Net Banking',
          description: 'All major banks supported',
          icon: FaUniversity,
          logos: ['sbi', 'hdfc', 'icici', 'axis']
        },
        {
          id: 'wallets',
          name: 'Wallets',
          description: 'Digital wallets',
          icon: FaWallet,
          logos: ['paytm', 'mobikwik', 'freecharge']
        }
      ]
    }
  ]

  const handlePaymentChange = (method) => {
    setPaymentMethod(method)
  }

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded)
  }

  // Get selected payment method name
  const getSelectedPaymentName = () => {
    const selected = paymentOptions.find(option => option.id === paymentMethod)
    return selected ? selected.name : 'Select payment method'
  }

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      {/* Header */}
      <div
        className="flex items-center justify-between p-6 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={toggleExpanded}
      >
        <div className="flex items-center space-x-2">
          <FaWallet className="w-5 h-5 text-gray-600" />
          <h2 className="text-lg font-semibold text-gray-900">Payment Method</h2>
          {paymentMethod && !isExpanded && (
            <FaCheckCircle className="w-4 h-4 text-green-600 ml-2" />
          )}
        </div>
        <div className="flex items-center space-x-2">
          {!isExpanded && paymentMethod && (
            <span className="text-sm text-gray-600">
              {getSelectedPaymentName()}
            </span>
          )}
          {isExpanded ? (
            <FaChevronUp className="w-4 h-4 text-gray-400" />
          ) : (
            <FaChevronDown className="w-4 h-4 text-gray-400" />
          )}
        </div>
      </div>

      {/* Collapsible Content */}
      {isExpanded && (
        <div className="px-6 pb-6 border-t border-gray-100">
          {/* Payment Options */}
          <div className="space-y-6 mt-6">
            {paymentOptions.map((option) => {
              const IconComponent = option.icon
              return (
                <div key={option.id} className="space-y-4">
                  {/* Main Payment Option */}
                  <label
                    className={`
                      relative flex items-start space-x-3 p-4 border rounded-lg cursor-pointer transition-all
                      ${paymentMethod === option.id
                        ? 'border-primary bg-primary/5 ring-2 ring-primary/20'
                        : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }
                    `}
                  >
                    <input
                      type="radio"
                      name="paymentMethod"
                      value={option.id}
                      checked={paymentMethod === option.id}
                      onChange={() => handlePaymentChange(option.id)}
                      className="mt-1 h-4 w-4 text-primary border-gray-300 focus:ring-primary"
                    />

                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <IconComponent className="w-5 h-5 text-gray-600" />
                          <div className="text-sm font-medium text-gray-900">
                            {option.name}
                          </div>
                          {option.recommended && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                              Recommended
                            </span>
                          )}
                        </div>
                        {paymentMethod === option.id && (
                          <FaCheckCircle className="w-4 h-4 text-primary" />
                        )}
                      </div>

                      <div className="text-sm text-gray-600 mt-1">
                        {option.description}
                      </div>

                      {/* Note */}
                      {option.note && (
                        <div className="text-xs text-gray-500 mt-2 italic">
                          {option.note}
                        </div>
                      )}
                    </div>
                  </label>

                  {/* Sub Payment Methods for Online Payment */}
                  {option.id === 'razorpay' && paymentMethod === 'razorpay' && (
                    <div className="ml-7 space-y-3">
                      <h4 className="text-sm font-medium text-gray-900 mb-3">Choose payment method:</h4>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        {option.subMethods.map((subMethod) => {
                          const SubIcon = subMethod.icon
                          return (
                            <label
                              key={subMethod.id}
                              className={`
                                flex items-center space-x-3 p-3 border rounded-lg cursor-pointer transition-all
                                ${selectedSubMethod === subMethod.id
                                  ? 'border-primary bg-primary/5'
                                  : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                                }
                              `}
                            >
                              <input
                                type="radio"
                                name="subPaymentMethod"
                                value={subMethod.id}
                                checked={selectedSubMethod === subMethod.id}
                                onChange={() => setSelectedSubMethod(subMethod.id)}
                                className="h-4 w-4 text-primary border-gray-300 focus:ring-primary"
                              />
                              <SubIcon className="w-4 h-4 text-gray-600" />
                              <div className="flex-1">
                                <div className="text-sm font-medium text-gray-900">
                                  {subMethod.name}
                                </div>
                                <div className="text-xs text-gray-500">
                                  {subMethod.description}
                                </div>
                              </div>
                              {selectedSubMethod === subMethod.id && (
                                <FaCheckCircle className="w-4 h-4 text-primary" />
                              )}
                            </label>
                          )
                        })}
                      </div>
                    </div>
                  )}
                </div>
              )
            })}
          </div>

      {/* Payment Error */}
      {errors.payment && (
        <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-600 text-sm">{errors.payment}</p>
        </div>
      )}

          {/* Security Notice */}
          <div className="mt-6 p-4 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
            <div className="flex items-start space-x-3">
              <FaShieldAlt className="w-5 h-5 text-green-600 mt-0.5" />
              <div>
                <div className="text-sm font-medium text-gray-900 mb-1">
                  100% Secure Payments
                </div>
                <div className="text-xs text-gray-600 space-y-1">
                  <div>• All payments are secured with 256-bit SSL encryption</div>
                  <div>• Powered by Razorpay - India's most trusted payment gateway</div>
                  <div>• Your card details are never stored on our servers</div>
                  <div className="text-blue-600 font-medium mt-2">
                    <strong>Demo Mode:</strong> Test integration - Backend verification pending
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default PaymentOptions
