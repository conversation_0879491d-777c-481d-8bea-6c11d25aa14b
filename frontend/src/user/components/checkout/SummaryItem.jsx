import React from 'react'

const SummaryItem = ({ item }) => {
  // Format currency
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR'
    }).format(amount)
  }

  return (
    <div className="flex items-center space-x-4 py-4 border-b border-gray-100 last:border-b-0">
      {/* Product Image */}
      <div className="relative flex-shrink-0">
        <img
          src={item.image}
          alt={item.name}
          className="w-16 h-16 object-cover rounded-lg border border-gray-200"
          onError={(e) => {
            e.target.src = 'https://via.placeholder.com/64x64?text=No+Image'
          }}
        />
        {/* Quantity Badge */}
        {item.quantity > 1 && (
          <span className="absolute -top-2 -right-2 bg-gray-600 text-white text-xs font-medium rounded-full w-5 h-5 flex items-center justify-center">
            {item.quantity}
          </span>
        )}
      </div>

      {/* Product Details */}
      <div className="flex-1 min-w-0">
        <h4 className="text-sm font-medium text-gray-900 truncate">
          {item.name}
        </h4>
        <div className="flex items-center justify-between mt-1">
          <div className="text-sm text-gray-600">
            {item.quantity > 1 ? (
              <span>{item.quantity} × {formatCurrency(item.price)}</span>
            ) : (
              <span>{formatCurrency(item.price)}</span>
            )}
          </div>
          <div className="text-sm font-semibold text-gray-900">
            {formatCurrency(item.price * item.quantity)}
          </div>
        </div>
      </div>
    </div>
  )
}

export default SummaryItem
