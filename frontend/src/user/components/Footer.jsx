import React, { useState } from 'react'
import { Link } from 'react-router-dom'
import { FaFacebook, FaInstagram, FaTwitter, FaEnvelope, FaPhone, FaMapMarkerAlt } from 'react-icons/fa'
import Button from '../../shared/ui/Button'
import Input from '../../shared/ui/Input'
import logo from '../../assets/Logo1.png'
import { encodeCategoryForURL } from '../../utils/categoryUtils'

const Footer = () => {
  const [email, setEmail] = useState('')

  const handleNewsletterSubmit = (e) => {
    e.preventDefault()
    if (email) {
      console.log('Newsletter subscription:', email)
      alert('Thank you for subscribing to our newsletter!')
      setEmail('')
    }
  }

  const quickLinks = [
    { name: 'About Us', path: '/about' },
    { name: 'Contact', path: '/contact' },
    { name: 'Privacy Policy', path: '/privacy' },
    { name: 'Returns', path: '/returns' },
    { name: 'Terms of Service', path: '/terms' },
    { name: 'FAQ', path: '/faq' }
  ]

  const categories = [
    { name: 'Statue', path: `/category/${encodeCategoryForURL('Statue')}` },
    { name: 'Poshak', path: `/category/${encodeCategoryForURL('Poshak')}` },
    { name: 'Brass Items', path: `/category/${encodeCategoryForURL('Brass Items')}` },
    { name: 'Jwellery Set', path: `/category/${encodeCategoryForURL('Jwellery Set')}` },
    { name: 'Jhoola', path: `/category/${encodeCategoryForURL('Jhoola')}` },
    { name: 'Bansi', path: `/category/${encodeCategoryForURL('Bansi')}` }
  ]

  const socialLinks = [
    { icon: FaFacebook, href: 'https://facebook.com', color: 'hover:text-blue-600' },
    { icon: FaInstagram, href: 'https://instagram.com', color: 'hover:text-pink-600' },
    { icon: FaTwitter, href: 'https://twitter.com', color: 'hover:text-blue-400' }
  ]

  return (
    <footer className="bg-text text-white">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">

          {/* Company Info */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <img src={logo} alt="ShringarHub" className="w-12 h-12 rounded-full" />
              <h3 className="text-2xl font-bold text-white">ShringarHub</h3>
            </div>
            <p className="text-gray-300 leading-relaxed">
              Your trusted marketplace for authentic divine items. We bring spirituality
              and devotion to your home with our carefully curated collection of sacred artifacts.
            </p>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-white">Quick Links</h4>
            <ul className="space-y-2">
              {quickLinks.map((link, index) => (
                <li key={index}>
                  <Link
                    to={link.path}
                    className="text-gray-300 hover:text-primary transition-colors duration-200 text-sm"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Categories */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-white">Categories</h4>
            <ul className="space-y-2">
              {categories.map((category, index) => (
                <li key={index}>
                  <Link
                    to={category.path}
                    className="text-gray-300 hover:text-primary transition-colors duration-200 text-sm"
                  >
                    {category.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Newsletter Signup */}
          <div className="space-y-4">
            <h4 className="text-lg font-semibold text-white">Stay Updated</h4>
            <p className="text-gray-300 text-sm">
              Subscribe to our newsletter for the latest updates and special offers.
            </p>
            <form onSubmit={handleNewsletterSubmit} className="space-y-3">
              <Input
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
                className="w-full bg-gray-800 border-gray-700 text-white placeholder-gray-400 focus:border-primary focus:ring-primary"
              />
              <Button
                type="submit"
                variant="primary"
                className="w-full py-2 text-sm font-medium rounded-md hover:bg-red-700 transition-colors duration-200"
              >
                Subscribe
              </Button>
            </form>

            {/* Social Media Icons */}
            <div className="pt-4">
              <h5 className="text-sm font-medium text-white mb-3">Follow Us</h5>
              <div className="flex space-x-4">
                {socialLinks.map((social, index) => {
                  const IconComponent = social.icon
                  return (
                    <a
                      key={index}
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      className={`text-gray-300 ${social.color} transition-colors duration-200 transform hover:scale-110`}
                    >
                      <IconComponent className="w-6 h-6" />
                    </a>
                  )
                })}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Footer */}
      <div className="border-t border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="text-gray-400 text-sm text-center md:text-left">
              © {new Date().getFullYear()} ShringarHub. All rights reserved.
            </div>
            <div className="flex flex-wrap justify-center md:justify-end space-x-6 text-sm">
              <Link to="/privacy" className="text-gray-400 hover:text-primary transition-colors duration-200">
                Privacy Policy
              </Link>
              <Link to="/terms" className="text-gray-400 hover:text-primary transition-colors duration-200">
                Terms of Service
              </Link>
              <Link to="/returns" className="text-gray-400 hover:text-primary transition-colors duration-200">
                Return Policy
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}

export default Footer
