import { create } from 'zustand';

// localStorage keys
const STORAGE_KEYS = {
  USER: 'shringarhub_user',
  REMEMBER_EMAIL: 'shringarhub_remember_email',
  REMEMBER_ME: 'shringarhub_remember_me'
}

// Helper functions for localStorage
const storage = {
  getUser: () => {
    try {
      const user = localStorage.getItem(STORAGE_KEYS.USER)
      return user ? JSON.parse(user) : null
    } catch {
      return null
    }
  },

  setUser: (user) => {
    try {
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(user))
    } catch (error) {
      console.error('Failed to save user to localStorage:', error)
    }
  },

  removeUser: () => {
    localStorage.removeItem(STORAGE_KEYS.USER)
  },

  getRememberedEmail: () => {
    return localStorage.getItem(STORAGE_KEYS.REMEMBER_EMAIL) || ''
  },

  setRememberedEmail: (email) => {
    localStorage.setItem(STORAGE_KEYS.REMEMBER_EMAIL, email)
  },

  removeRememberedEmail: () => {
    localStorage.removeItem(STORAGE_KEYS.REMEMBER_EMAIL)
  },

  getRememberMe: () => {
    return localStorage.getItem(STORAGE_KEYS.REMEMBER_ME) === 'true'
  },

  setRememberMe: (remember) => {
    localStorage.setItem(STORAGE_KEYS.REMEMBER_ME, remember.toString())
  },

  removeRememberMe: () => {
    localStorage.removeItem(STORAGE_KEYS.REMEMBER_ME)
  }
}

// Generate a simple user ID
const generateUserId = () => {
  return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// Simulate async operations with small delays
const simulateAsync = (fn, delay = 500) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      try {
        resolve(fn())
      } catch (error) {
        reject(error)
      }
    }, delay)
  })
}

const useAuthStore = create((set, get) => ({
  // State
  currentUser: storage.getUser(),
  isAuthenticated: !!storage.getUser(),
  isLoading: false,
  error: null,
  rememberMe: storage.getRememberMe(),

  // Actions
  register: async (userData) => {
    set({ isLoading: true, error: null })

    try {
      await simulateAsync(() => {
        // Check if user already exists
        const registeredUsers = JSON.parse(localStorage.getItem('shringarhub_registered_users') || '[]')
        const existingUser = registeredUsers.find(user => user.email.toLowerCase() === userData.email.toLowerCase())

        if (existingUser) {
          throw new Error('User already exists with this email address. Please login instead.')
        }

        // Create new user object (excluding password for security)
        const newUser = {
          id: generateUserId(),
          name: userData.name,
          email: userData.email,
          phone: userData.phone || '',
          joinDate: new Date().toISOString(),
          subscribeNewsletter: userData.subscribeNewsletter || false
        }

        // Store user in a separate "registered users" storage for later login
        // Don't store in main user storage or set authenticated state
        registeredUsers.push(newUser)
        localStorage.setItem('shringarhub_registered_users', JSON.stringify(registeredUsers))

        // Keep user logged out after registration
        set({
          currentUser: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        })

        return newUser
      })

      return { success: true, user: null }
    } catch (error) {
      set({
        isLoading: false,
        error: error.message || 'Registration failed'
      })
      return { success: false, error: error.message }
    }
  },

  login: async (email, rememberMe = false) => {
    set({ isLoading: true, error: null })

    try {
      await simulateAsync(() => {
        // Check if user exists in registered users
        const registeredUsers = JSON.parse(localStorage.getItem('shringarhub_registered_users') || '[]')
        const foundUser = registeredUsers.find(user => user.email === email)

        if (!foundUser) {
          throw new Error('User not found. Please register first.')
        }

        // Store user in main localStorage for authentication
        storage.setUser(foundUser)

        // Handle remember me functionality
        if (rememberMe) {
          storage.setRememberedEmail(email)
          storage.setRememberMe(true)
        } else {
          storage.removeRememberedEmail()
          storage.setRememberMe(false)
        }

        // Set authenticated state
        set({
          currentUser: foundUser,
          isAuthenticated: true,
          isLoading: false,
          error: null,
          rememberMe: rememberMe
        })

        return foundUser
      })

      // Sync guest cart after successful login
      get().syncGuestCart?.()

      return { success: true, user: get().currentUser }
    } catch (error) {
      set({
        isLoading: false,
        error: error.message || 'Login failed'
      })
      return { success: false, error: error.message }
    }
  },

  logout: async () => {
    set({ isLoading: true })

    try {
      await simulateAsync(() => {
        const { rememberMe } = get()

        // Remove user from localStorage
        storage.removeUser()

        // If remember me is not enabled, clear remembered email
        if (!rememberMe) {
          storage.removeRememberedEmail()
          storage.removeRememberMe()
        }

        // Clear authenticated state
        set({
          currentUser: null,
          isAuthenticated: false,
          isLoading: false,
          error: null
        })
      })

      return { success: true }
    } catch (error) {
      set({
        isLoading: false,
        error: error.message || 'Logout failed'
      })
      return { success: false, error: error.message }
    }
  },

  // Initialize auth state from localStorage on app load
  initializeAuth: () => {
    try {
      const user = storage.getUser()
      const rememberMe = storage.getRememberMe()

      set({
        currentUser: user,
        isAuthenticated: !!user,
        rememberMe: rememberMe,
        isLoading: false,
        error: null
      })
    } catch (error) {
      console.error('Failed to initialize auth:', error)
      set({
        currentUser: null,
        isAuthenticated: false,
        rememberMe: false,
        isLoading: false,
        error: null
      })
    }
  },

  // Get remembered email if it exists
  getRememberedEmail: () => {
    return storage.getRememberedEmail()
  },

  // Clear error state
  clearError: () => {
    set({ error: null })
  },

  // Update user profile
  updateUser: (userData) => {
    const currentUser = get().currentUser
    if (currentUser) {
      const updatedUser = { ...currentUser, ...userData }
      storage.setUser(updatedUser)
      set({ currentUser: updatedUser })
      return { success: true, user: updatedUser }
    }
    return { success: false, error: 'No user logged in' }
  },

  // Set cart sync function (to be called by cart store)
  setSyncGuestCart: (syncFunction) => {
    set({ syncGuestCart: syncFunction })
  }
}));

export default useAuthStore;
