import {create} from 'zustand';
import { persist } from 'zustand/middleware'


const useCartStore = create(
    persist (
        (set, get) => ({
            cartItems: [],
            appliedCoupon: '',
            couponError: '',
            //Cart logic
            // add item to cart
            addToCart: (product, quantity) => set((state) => {
                const existing = state.cartItems.find(item => item.id === product.id);

                if(existing) {
                    // Increase quantity if product already in cart
                    return {
                        cartItems: state.cartItems.map(item =>
                        item.id === product.id
                            ? { ...item, quantity: item.quantity + quantity }
                            : item
                        ),
                    };
                } else {
                    // Add new product with quantity
                    return { cartItems: [...state.cartItems, { ...product, quantity }] };
                }
            }),

            // Remove a product by ID
            removeFromCart: (productId) => set((state) => {
                const newCartItems = state.cartItems.filter((item) => item.id !== productId);

                // Clear coupon if cart becomes empty
                if (newCartItems.length === 0) {
                    // Import and clear coupon store
                    import('./couponStore').then(module => {
                        const useCouponStore = module.default;
                        useCouponStore.getState().clearCoupon();
                    });
                }

                return { cartItems: newCartItems };
            }),

            // increase quantity of an item
            increaseQuantity: (productId) =>
            set((state) => ({
            cartItems: state.cartItems.map((item) =>
                item.id === productId
                ? { ...item, quantity: item.quantity + 1 }
                : item
            ),
            })),

            // decrease quantity of an item
            decreaseQuantity: (productId) =>
            set((state) => {
                const newCartItems = state.cartItems
                    .map((item) =>
                    item.id === productId
                        ? { ...item, quantity: item.quantity - 1 }
                        : item
                    )
                    .filter((item) => item.quantity > 0); // Remove if quantity goes to 0

                // Clear coupon if cart becomes empty
                if (newCartItems.length === 0) {
                    import('./couponStore').then(module => {
                        const useCouponStore = module.default;
                        useCouponStore.getState().clearCoupon();
                    });
                }

                return { cartItems: newCartItems };
            }),


            //clear the entire cart
            clearCart : () => {
                // Clear coupon when cart is cleared
                import('./couponStore').then(module => {
                    const useCouponStore = module.default;
                    useCouponStore.getState().clearCoupon();
                });

                set({cartItems: [] });
            },

            // Get subtotal
            getSubtotal : () => {
                const items = get().cartItems;
                return items.reduce((acc, item) => acc + item.price * item.quantity, 0);
            },

            // Get discount based on coupon code
            getDiscount: (couponCode) => {
                const subtotal = get().getSubtotal();
                if(couponCode === 'GOD10') {
                    return subtotal * 0.1;  // 10% off
                }
                return 0;
            },

            // Get total after discount
            getTotal : (discount = 0 ) => {
                const subtotal = get().getSubtotal();
                return subtotal - discount
            },

            // ✅ Selector: total quantity of items
            getTotalQuantity: () =>
            get().cartItems.reduce((total, item) => total + item.quantity, 0),

            // Coupon management
            applyCoupon: (couponCode) => {
                const validDiscount = get().getDiscount(couponCode);
                if (validDiscount > 0) {
                    set({
                        appliedCoupon: couponCode.toUpperCase(),
                        couponError: ''
                    });
                    return { success: true, message: 'Coupon applied successfully!' };
                } else {
                    set({
                        appliedCoupon: '',
                        couponError: 'Invalid coupon code. Please try again.'
                    });
                    return { success: false, message: 'Invalid coupon code. Please try again.' };
                }
            },

            removeCoupon: () => set({
                appliedCoupon: '',
                couponError: ''
            }),

            clearCouponError: () => set({ couponError: '' }),

            // Get applied discount amount
            getAppliedDiscount: () => {
                const appliedCoupon = get().appliedCoupon;
                return appliedCoupon ? get().getDiscount(appliedCoupon) : 0;
            },

            // Get final total with applied discount
            getFinalTotal: () => {
                const subtotal = get().getSubtotal();
                const discount = get().getAppliedDiscount();
                return subtotal - discount;
            },

            // Sync guest cart when user logs in
            syncGuestCart: () => {
                // This function is called when user logs in
                // Guest cart data is already in localStorage via persist middleware
                // No additional action needed as cart persists automatically
                console.log('Guest cart synced for authenticated user');
            },

            // Initialize cart sync with auth store
            initializeCartSync: () => {
                // Register sync function with auth store
                const authStore = useAuthStore.getState();
                if (authStore.setSyncGuestCart) {
                    authStore.setSyncGuestCart(get().syncGuestCart);
                }
            },
        }),
        {
            name: 'cart-storage', //key for local Storage
        }

        
    )
)

export default useCartStore;