import { create } from 'zustand'
import { persist } from 'zustand/middleware'

// Mock coupon data - replace with API calls in production
const VALID_COUPONS = {
  'SAVE10': { code: 'SAVE10', discount: 10, type: 'percentage', description: '10% off' },
  'FLAT50': { code: 'FLAT50', discount: 50, type: 'fixed', description: '₹50 off' },
  'WELCOME15': { code: 'WELCOME15', discount: 15, type: 'percentage', description: '15% off' },
  'NEWUSER': { code: 'NEWUSER', discount: 100, type: 'fixed', description: '₹100 off' },
  'FESTIVAL20': { code: 'FESTIVAL20', discount: 20, type: 'percentage', description: '20% off' }
}

const useCouponStore = create(
  persist(
    (set, get) => ({
      // State
      couponCode: '',
      appliedCoupon: null,
      discountAmount: 0,
      isApplying: false,
      error: '',

      // Actions
      setCouponCode: (code) => {
        set({ 
          couponCode: code.toUpperCase(),
          error: '' // Clear error when typing
        })
      },

      applyCoupon: async (code, subtotal) => {
        const upperCode = code.toUpperCase().trim()
        
        if (!upperCode) {
          set({ error: 'Please enter a coupon code' })
          return false
        }

        set({ isApplying: true, error: '' })

        try {
          // Simulate API call delay
          await new Promise(resolve => setTimeout(resolve, 500))

          // Check if coupon exists
          const coupon = VALID_COUPONS[upperCode]
          if (!coupon) {
            set({ 
              error: 'Invalid coupon code',
              isApplying: false 
            })
            return false
          }

          // Check if coupon is already applied
          const currentAppliedCoupon = get().appliedCoupon
          if (currentAppliedCoupon && currentAppliedCoupon.code === upperCode) {
            set({ 
              error: 'This coupon is already applied',
              isApplying: false 
            })
            return false
          }

          // Calculate discount amount
          let discountAmount = 0
          if (coupon.type === 'percentage') {
            discountAmount = Math.round((subtotal * coupon.discount) / 100)
          } else if (coupon.type === 'fixed') {
            discountAmount = Math.min(coupon.discount, subtotal) // Don't exceed subtotal
          }

          // Apply coupon
          set({
            appliedCoupon: coupon,
            discountAmount,
            couponCode: '',
            isApplying: false,
            error: ''
          })

          return true
        } catch (error) {
          console.error('Error applying coupon:', error)
          set({ 
            error: 'Failed to apply coupon. Please try again.',
            isApplying: false 
          })
          return false
        }
      },

      clearCoupon: () => {
        set({
          appliedCoupon: null,
          discountAmount: 0,
          couponCode: '',
          error: ''
        })
      },

      // Helper to get final total after discount
      getFinalTotal: (subtotal) => {
        const discountAmount = get().discountAmount
        return Math.max(0, subtotal - discountAmount)
      },

      // Clear error
      clearError: () => {
        set({ error: '' })
      },

      // Clear coupon if cart becomes empty (called from cart store)
      clearCouponIfCartEmpty: (cartItems) => {
        if (!cartItems || cartItems.length === 0) {
          const currentState = get()
          if (currentState.appliedCoupon) {
            set({
              appliedCoupon: null,
              discountAmount: 0,
              couponCode: '',
              error: ''
            })
          }
        }
      }
    }),
    {
      name: 'shringarhub-coupon', // localStorage key
      partialize: (state) => ({
        appliedCoupon: state.appliedCoupon,
        discountAmount: state.discountAmount
      })
    }
  )
)

export default useCouponStore
