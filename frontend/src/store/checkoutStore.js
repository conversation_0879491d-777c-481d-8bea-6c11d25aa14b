import { create } from 'zustand'
import { persist } from 'zustand/middleware'

const useCheckoutStore = create(
  persist(
    (set, get) => ({
      // Shipping Address
      shippingAddress: {
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        address: '',
        apartment: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'India'
      },

      // Billing Address
      billingAddress: {
        firstName: '',
        lastName: '',
        email: '',
        phone: '',
        address: '',
        apartment: '',
        city: '',
        state: '',
        zipCode: '',
        country: 'India'
      },

      // Billing address options
      useSameAddress: true,

      // Payment method
      paymentMethod: 'upi', // 'upi', 'card', 'cod'

      // Form validation errors
      errors: {},

      // Loading states
      isSubmitting: false,

      // Actions
      setShippingAddress: (address) => set({ 
        shippingAddress: { ...get().shippingAddress, ...address },
        errors: { ...get().errors, shipping: {} }
      }),

      setBillingAddress: (address) => set({ 
        billingAddress: { ...get().billingAddress, ...address },
        errors: { ...get().errors, billing: {} }
      }),

      setUseSameAddress: (useSame) => {
        set({ useSameAddress: useSame })
        if (useSame) {
          // Copy shipping to billing when same address is selected
          set({ billingAddress: { ...get().shippingAddress } })
        }
      },

      setPaymentMethod: (method) => set({ paymentMethod: method }),

      setErrors: (errors) => set({ errors }),

      clearErrors: () => set({ errors: {} }),

      // Initialize with user data
      initializeWithUserData: (userData) => {
        if (userData) {
          const { name, email, phone } = userData
          const [firstName = '', lastName = ''] = name ? name.split(' ') : ['', '']

          set({
            shippingAddress: {
              ...get().shippingAddress,
              firstName,
              lastName,
              email: email || '',
              phone: phone || ''
            }
          })
        }
      },

      // Initialize with saved address from address store
      initializeWithSavedAddress: (address) => {
        if (address) {
          const [firstName = '', lastName = ''] = address.fullName ? address.fullName.split(' ') : ['', '']

          set({
            shippingAddress: {
              ...get().shippingAddress,
              firstName,
              lastName,
              phone: address.phoneNumber || '',
              address: address.addressLine1 || '',
              apartment: address.addressLine2 || '',
              city: address.city || '',
              state: address.state || '',
              zipCode: address.zipCode || '',
              country: 'India'
            }
          })
        }
      },

      // Validate forms
      validateShipping: () => {
        const { shippingAddress } = get()
        const errors = {}

        if (!shippingAddress.firstName.trim()) errors.firstName = 'First name is required'
        if (!shippingAddress.lastName.trim()) errors.lastName = 'Last name is required'
        if (!shippingAddress.email.trim()) errors.email = 'Email is required'
        else if (!/\S+@\S+\.\S+/.test(shippingAddress.email)) errors.email = 'Email is invalid'
        if (!shippingAddress.phone.trim()) errors.phone = 'Phone number is required'
        else if (!/^\d{10}$/.test(shippingAddress.phone.replace(/\D/g, ''))) errors.phone = 'Phone number must be 10 digits'
        if (!shippingAddress.address.trim()) errors.address = 'Address is required'
        if (!shippingAddress.city.trim()) errors.city = 'City is required'
        if (!shippingAddress.state.trim()) errors.state = 'State is required'
        if (!shippingAddress.zipCode.trim()) errors.zipCode = 'ZIP code is required'
        else if (!/^\d{6}$/.test(shippingAddress.zipCode)) errors.zipCode = 'ZIP code must be 6 digits'

        set({ errors: { ...get().errors, shipping: errors } })
        return Object.keys(errors).length === 0
      },

      validateBilling: () => {
        const { billingAddress, useSameAddress } = get()
        if (useSameAddress) return true

        const errors = {}

        if (!billingAddress.firstName.trim()) errors.firstName = 'First name is required'
        if (!billingAddress.lastName.trim()) errors.lastName = 'Last name is required'
        if (!billingAddress.email.trim()) errors.email = 'Email is required'
        else if (!/\S+@\S+\.\S+/.test(billingAddress.email)) errors.email = 'Email is invalid'
        if (!billingAddress.phone.trim()) errors.phone = 'Phone number is required'
        else if (!/^\d{10}$/.test(billingAddress.phone.replace(/\D/g, ''))) errors.phone = 'Phone number must be 10 digits'
        if (!billingAddress.address.trim()) errors.address = 'Address is required'
        if (!billingAddress.city.trim()) errors.city = 'City is required'
        if (!billingAddress.state.trim()) errors.state = 'State is required'
        if (!billingAddress.zipCode.trim()) errors.zipCode = 'ZIP code is required'
        else if (!/^\d{6}$/.test(billingAddress.zipCode)) errors.zipCode = 'ZIP code must be 6 digits'

        set({ errors: { ...get().errors, billing: errors } })
        return Object.keys(errors).length === 0
      },

      // Validate entire checkout
      validateCheckout: () => {
        const isShippingValid = get().validateShipping()
        const isBillingValid = get().validateBilling()
        const { paymentMethod } = get()

        if (!paymentMethod) {
          set({ errors: { ...get().errors, payment: 'Please select a payment method' } })
          return false
        }

        return isShippingValid && isBillingValid
      },

      // Get checkout payload
      getCheckoutPayload: () => {
        const { shippingAddress, billingAddress, useSameAddress, paymentMethod } = get()
        
        return {
          shipping: shippingAddress,
          billing: useSameAddress ? shippingAddress : billingAddress,
          paymentMethod,
          useSameAddress
        }
      },

      // Reset checkout state
      resetCheckout: () => set({
        shippingAddress: {
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          address: '',
          apartment: '',
          city: '',
          state: '',
          zipCode: '',
          country: 'India'
        },
        billingAddress: {
          firstName: '',
          lastName: '',
          email: '',
          phone: '',
          address: '',
          apartment: '',
          city: '',
          state: '',
          zipCode: '',
          country: 'India'
        },
        useSameAddress: true,
        paymentMethod: 'upi',
        errors: {},
        isSubmitting: false
      })
    }),
    {
      name: 'shringarhub-checkout',
      partialize: (state) => ({
        shippingAddress: state.shippingAddress,
        billingAddress: state.billingAddress,
        useSameAddress: state.useSameAddress,
        paymentMethod: state.paymentMethod
      })
    }
  )
)

export default useCheckoutStore
