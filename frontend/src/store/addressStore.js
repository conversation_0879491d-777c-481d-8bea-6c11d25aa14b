import { create } from 'zustand';
import { persist } from 'zustand/middleware';

const useAddressStore = create(
  persist(
    (set, get) => ({
      // Shipping addresses
      addresses: [],
      selectedAddressId: null,

      // Billing addresses (separate from shipping)
      billingAddresses: [],
      selectedBillingAddressId: null,

      addAddress: (address) => {
        const id = Date.now().toString();
        const addresses = get().addresses;
        const newAddress = {
          ...address,
          id,
          // If this is set as default or it's the first address, make it default
          isDefault: address.isDefault || addresses.length === 0
        };

        // If new address is default, remove default from others
        const updatedAddresses = addresses.map(addr => ({
          ...addr,
          isDefault: newAddress.isDefault ? false : addr.isDefault
        }));

        set({
          addresses: [...updatedAddresses, newAddress],
          selectedAddressId: id,
        });
      },

      updateAddress: (addressId, addressData) => {
        const addresses = get().addresses;
        const updatedAddresses = addresses.map(addr => {
          if (addr.id === addressId) {
            const updatedAddr = { ...addressData, id: addressId };

            // If this address is being set as default, remove default from others
            if (updatedAddr.isDefault) {
              addresses.forEach(a => {
                if (a.id !== addressId) a.isDefault = false;
              });
            }

            return updatedAddr;
          }
          return addr;
        });

        set({ addresses: updatedAddresses });
      },

      deleteAddress: (addressId) => {
        const addresses = get().addresses;
        const addressToDelete = addresses.find(addr => addr.id === addressId);
        const filteredAddresses = addresses.filter(addr => addr.id !== addressId);

        // If deleted address was default and there are other addresses, make first one default
        if (addressToDelete?.isDefault && filteredAddresses.length > 0) {
          filteredAddresses[0].isDefault = true;
        }

        set({
          addresses: filteredAddresses,
          selectedAddressId: get().selectedAddressId === addressId ? null : get().selectedAddressId
        });
      },

      setDefaultAddress: (addressId) => {
        const addresses = get().addresses;
        const updatedAddresses = addresses.map(addr => ({
          ...addr,
          isDefault: addr.id === addressId
        }));

        set({ addresses: updatedAddresses });
      },

      selectAddress: (id) => set({ selectedAddressId: id }),

      // Get default address
      getDefaultAddress: () => {
        const addresses = get().addresses;
        return addresses.find(addr => addr.isDefault) || addresses[0] || null;
      },

      // Billing Address Management
      addBillingAddress: (address) => {
        const id = Date.now().toString();
        const billingAddresses = get().billingAddresses;
        const newAddress = {
          ...address,
          id,
          // Billing addresses don't have default concept, just add as-is
        };

        set({
          billingAddresses: [...billingAddresses, newAddress],
          selectedBillingAddressId: id,
        });
      },

      updateBillingAddress: (addressId, addressData) => {
        const billingAddresses = get().billingAddresses;
        const updatedAddresses = billingAddresses.map(addr => {
          if (addr.id === addressId) {
            return { ...addressData, id: addressId };
          }
          return addr;
        });

        set({ billingAddresses: updatedAddresses });
      },

      deleteBillingAddress: (addressId) => {
        const billingAddresses = get().billingAddresses;
        const filteredAddresses = billingAddresses.filter(addr => addr.id !== addressId);

        set({
          billingAddresses: filteredAddresses,
          selectedBillingAddressId: get().selectedBillingAddressId === addressId ? null : get().selectedBillingAddressId
        });
      },

      selectBillingAddress: (id) => set({ selectedBillingAddressId: id }),

      // Get selected billing address
      getSelectedBillingAddress: () => {
        const billingAddresses = get().billingAddresses;
        const selectedId = get().selectedBillingAddressId;
        return billingAddresses.find(addr => addr.id === selectedId) || null;
      },
    }),
    {
      name: 'shringarhub-addresses', // localStorage key
    }
  )
);

export default useAddressStore;
