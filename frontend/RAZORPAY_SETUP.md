# Razorpay Setup Instructions

## 🔐 Secure Environment Variable Configuration

This project uses environment variables to keep Razorpay keys secure and prevent them from being pushed to GitHub.

## 📋 Setup Steps

### 1. Get Your Razorpay Keys

1. Go to [Razorpay Dashboard](https://dashboard.razorpay.com/)
2. Sign up for a free account (if you don't have one)
3. Navigate to **Settings → API Keys**
4. Click **"Generate Test Key"**
5. Copy the **Key ID** (format: `rzp_test_xxxxxxxxxx`)

### 2. Configure Environment Variables

1. Copy the example environment file:
   ```bash
   cp .env.example .env
   ```

2. Edit the `.env` file and add your actual Razorpay key:
   ```env
   VITE_RAZORPAY_TEST_KEY=rzp_test_your_actual_key_here
   VITE_RAZORPAY_LIVE_KEY=rzp_live_your_live_key_here
   VITE_APP_ENV=development
   ```

### 3. Restart Development Server

After updating the `.env` file, restart your development server:
```bash
npm run dev
```

## 🔒 Security Features

- ✅ **Environment Variables**: Keys are stored in `.env` file
- ✅ **Git Ignored**: `.env` file is excluded from version control
- ✅ **No Hardcoded Keys**: No sensitive data in source code
- ✅ **Example File**: `.env.example` shows required variables without exposing keys

## 🚀 Deployment

For production deployment:

1. Set environment variables in your hosting platform:
   - **Vercel**: Project Settings → Environment Variables
   - **Netlify**: Site Settings → Environment Variables
   - **Heroku**: Settings → Config Vars

2. Update the environment:
   ```env
   VITE_RAZORPAY_LIVE_KEY=rzp_live_your_live_key
   VITE_APP_ENV=production
   ```

## 💳 Test Payment

Use these test card details in the Razorpay modal:

```
Card Number: 4111 1111 1111 1111
Expiry: Any future date (e.g., 12/25)
CVV: Any 3 digits (e.g., 123)
Name: Any name
```

## 🔍 Troubleshooting

### "Razorpay key not configured" Error
- Check if `.env` file exists
- Verify the key format: `rzp_test_xxxxxxxxxx`
- Restart the development server

### 401 Unauthorized Error
- Verify your Razorpay key is correct
- Check if the key is active in Razorpay dashboard
- Ensure you're using the test key for development

## 📁 File Structure

```
frontend/
├── .env                 # Your actual keys (git ignored)
├── .env.example         # Template file (safe to commit)
├── .gitignore          # Excludes .env from git
├── src/
│   └── config/
│       └── razorpay.js # Configuration using env variables
└── RAZORPAY_SETUP.md   # This file
```

## ⚠️ Important Notes

- **Never commit** `.env` file to git
- **Always use** environment variables for sensitive data
- **Test thoroughly** before deploying to production
- **Keep keys secure** and rotate them regularly
