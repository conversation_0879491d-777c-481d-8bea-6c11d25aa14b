import { PrismaClient } from './src/generated/prisma/index.js'
import dotenv from 'dotenv'

dotenv.config()

async function testConnection() {
  console.log('🔍 Testing database connection...')
  console.log('📍 Database URL:', process.env.DATABASE_URL ? 'Set' : 'Not set')
  
  const prisma = new PrismaClient()
  
  try {
    // Test basic connection
    await prisma.$connect()
    console.log('✅ Database connection successful!')
    
    // Test a simple query
    const result = await prisma.$queryRaw`SELECT 1 as test`
    console.log('✅ Query test successful:', result)
    
  } catch (error) {
    console.error('❌ Database connection failed:')
    console.error('Error code:', error.code)
    console.error('Error message:', error.message)
    
    if (error.code === 'P1001') {
      console.log('\n💡 Troubleshooting tips:')
      console.log('1. Check if your Supabase project is active (not paused)')
      console.log('2. Verify the connection string in your .env file')
      console.log('3. Make sure your IP is allowed (Supabase allows all by default)')
      console.log('4. Check if you have internet connectivity')
    }
  } finally {
    await prisma.$disconnect()
  }
}

testConnection()
