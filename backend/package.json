{"name": "backend", "version": "1.0.0", "description": "", "type": "module", "main": "server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "db:generate": "prisma generate", "db:migrate": "prisma migrate dev", "db:reset": "prisma migrate reset", "db:studio": "prisma studio", "db:seed": "node src/utils/seed.js"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@prisma/client": "^6.11.1", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^16.5.0", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "pg": "^8.16.1"}, "devDependencies": {"nodemon": "^3.1.10", "prisma": "^6.10.0"}}