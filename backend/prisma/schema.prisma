// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums for better type safety
enum UserRole {
  USER
  VENDOR
  ADMIN
}

enum OrderStatus {
  PENDING
  CONFIRMED
  SHIPPED
  DELIVERED
  CANCELLED
}

enum PaymentStatus {
  PENDING
  COMPLETED
  FAILED
  REFUNDED
}

// User model with role-based access
model User {
  id        String   @id @default(cuid())
  email     String   @unique
  password  String
  firstName String
  lastName  String
  phone     String?
  role      UserRole @default(USER)
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  addresses Address[]
  orders    Order[]
  products  Product[] // For vendors
  reviews   Review[]

  @@map("users")
}

// Address model for shipping/billing
model Address {
  id       String  @id @default(cuid())
  userId   String
  type     String  // 'shipping' or 'billing'
  street   String
  city     String
  state    String
  pinCode  String
  country  String  @default("India")
  isDefault Boolean @default(false)

  // Relations
  user   User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  orders Order[]

  @@map("addresses")
}

// Product model
model Product {
  id          String   @id @default(cuid())
  name        String
  description String
  price       Decimal  @db.Decimal(10, 2)
  category    String
  images      String[] // Array of image URLs
  stock       Int      @default(0)
  isActive    Boolean  @default(true)
  vendorId    String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  vendor     User        @relation(fields: [vendorId], references: [id])
  orderItems OrderItem[]
  reviews    Review[]

  @@map("products")
}

// Order model
model Order {
  id            String        @id @default(cuid())
  userId        String
  addressId     String
  status        OrderStatus   @default(PENDING)
  paymentStatus PaymentStatus @default(PENDING)
  totalAmount   Decimal       @db.Decimal(10, 2)
  createdAt     DateTime      @default(now())
  updatedAt     DateTime      @updatedAt

  // Relations
  user      User        @relation(fields: [userId], references: [id])
  address   Address     @relation(fields: [addressId], references: [id])
  items     OrderItem[]
  payment   Payment?

  @@map("orders")
}

// Order items (products in an order)
model OrderItem {
  id        String  @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  price     Decimal @db.Decimal(10, 2) // Price at time of order

  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

// Payment model
model Payment {
  id              String        @id @default(cuid())
  orderId         String        @unique
  amount          Decimal       @db.Decimal(10, 2)
  paymentMethod   String        // 'razorpay', 'cod', etc.
  transactionId   String?       // External payment gateway transaction ID
  status          PaymentStatus @default(PENDING)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  order Order @relation(fields: [orderId], references: [id])

  @@map("payments")
}

// Review model
model Review {
  id        String   @id @default(cuid())
  userId    String
  productId String
  rating    Int      // 1-5 stars
  comment   String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user    User    @relation(fields: [userId], references: [id])
  product Product @relation(fields: [productId], references: [id])

  // Ensure one review per user per product
  @@unique([userId, productId])
  @@map("reviews")
}
