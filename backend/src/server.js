import express from 'express'
import cors from 'cors'
import dotenv from 'dotenv'
import apiRoutes from './routes/index.js'
import prisma from './config/database.js'

// Load environment variables
dotenv.config()

const app = express()
const PORT = process.env.PORT || 5000

// Middleware
app.use(cors({
  origin: process.env.FRONTEND_URL || 'http://localhost:3000',
  credentials: true
}))
app.use(express.json({ limit: '10mb' }))
app.use(express.urlencoded({ extended: true }))

// Request logging middleware (development only)
if (process.env.NODE_ENV === 'development') {
  app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`)
    next()
  })
}

// Routes
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'ShringarHub API Server is running!',
    version: '1.0.0',
    environment: process.env.NODE_ENV
  })
})

// API routes
app.use('/api', apiRoutes)

// Global error handler
app.use((error, req, res, next) => {
  console.error('Global error handler:', error)

  res.status(error.status || 500).json({
    success: false,
    message: error.message || 'Internal server error',
    ...(process.env.NODE_ENV === 'development' && { stack: error.stack })
  })
})

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    success: false,
    message: `Route ${req.originalUrl} not found`
  })
})

// Graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully...')
  await prisma.$disconnect()
  process.exit(0)
})

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully...')
  await prisma.$disconnect()
  process.exit(0)
})

// Start server
app.listen(PORT, () => {
  console.log('🚀 ShringarHub Backend Server Started!')
  console.log(`📍 Server running on port ${PORT}`)
  console.log(`🌍 Environment: ${process.env.NODE_ENV}`)
  console.log(`🔗 API Base URL: http://localhost:${PORT}/api`)
  console.log('📋 Available endpoints:')
  console.log('   GET  / - Server status')
  console.log('   GET  /api/health - Health check')
  console.log('   POST /api/auth/register - User registration')
  console.log('   POST /api/auth/login - User login')
  console.log('   GET  /api/auth/profile - Get user profile (protected)')
})
