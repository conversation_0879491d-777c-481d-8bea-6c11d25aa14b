// Validation middleware for request data
export const validateRegister = (req, res, next) => {
  const { email, password, firstName, lastName } = req.body
  const errors = []

  // Email validation
  if (!email) {
    errors.push('Email is required')
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
    errors.push('Please provide a valid email')
  }

  // Password validation
  if (!password) {
    errors.push('Password is required')
  } else if (password.length < 6) {
    errors.push('Password must be at least 6 characters long')
  }

  // Name validation
  if (!firstName || firstName.trim().length < 2) {
    errors.push('First name must be at least 2 characters long')
  }

  if (!lastName || lastName.trim().length < 2) {
    errors.push('Last name must be at least 2 characters long')
  }

  // Role validation (if provided)
  if (req.body.role && !['USER', 'VENDOR', 'ADMIN'].includes(req.body.role)) {
    errors.push('Invalid role. Must be USER, VENDOR, or ADMIN')
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors
    })
  }

  next()
}

export const validateLogin = (req, res, next) => {
  const { email, password } = req.body
  const errors = []

  if (!email) {
    errors.push('Email is required')
  }

  if (!password) {
    errors.push('Password is required')
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors
    })
  }

  next()
}

export const validateProduct = (req, res, next) => {
  const { name, description, price, category, stock } = req.body
  const errors = []

  if (!name || name.trim().length < 2) {
    errors.push('Product name must be at least 2 characters long')
  }

  if (!description || description.trim().length < 10) {
    errors.push('Product description must be at least 10 characters long')
  }

  if (!price || isNaN(price) || parseFloat(price) <= 0) {
    errors.push('Price must be a positive number')
  }

  if (!category || category.trim().length < 2) {
    errors.push('Category is required')
  }

  if (stock !== undefined && (isNaN(stock) || parseInt(stock) < 0)) {
    errors.push('Stock must be a non-negative number')
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors
    })
  }

  next()
}

export const validateAddress = (req, res, next) => {
  const { street, city, state, pinCode, type } = req.body
  const errors = []

  if (!street || street.trim().length < 5) {
    errors.push('Street address must be at least 5 characters long')
  }

  if (!city || city.trim().length < 2) {
    errors.push('City is required')
  }

  if (!state || state.trim().length < 2) {
    errors.push('State is required')
  }

  if (!pinCode || !/^\d{6}$/.test(pinCode)) {
    errors.push('PIN code must be exactly 6 digits')
  }

  if (type && !['shipping', 'billing'].includes(type)) {
    errors.push('Address type must be either "shipping" or "billing"')
  }

  if (errors.length > 0) {
    return res.status(400).json({
      success: false,
      message: 'Validation failed',
      errors
    })
  }

  next()
}
