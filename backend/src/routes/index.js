import express from 'express'
import authRoutes from './authRoutes.js'

const router = express.Router()

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    message: 'ShringarHub API is running!',
    timestamp: new Date().toISOString(),
    environment: process.env.NODE_ENV
  })
})

// Mount route modules
router.use('/auth', authRoutes)

// 404 handler for API routes
router.use((req, res) => {
  res.status(404).json({
    success: false,
    message: `API route ${req.originalUrl} not found`
  })
})

export default router
