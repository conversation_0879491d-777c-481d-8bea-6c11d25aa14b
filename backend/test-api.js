// Simple test script to verify our API endpoints
// Run with: node test-api.js

const BASE_URL = 'http://localhost:5051/api'

async function testAPI() {
  console.log('🧪 Testing ShringarHub API...\n')

  try {
    // Test 1: Health check
    console.log('1️⃣ Testing health check...')
    const healthResponse = await fetch(`${BASE_URL}/health`)
    const healthData = await healthResponse.json()
    console.log('✅ Health check:', healthData.message)
    console.log('')

    // Test 2: Register a new user
    console.log('2️⃣ Testing user registration...')
    const registerData = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Test',
      lastName: 'User',
      phone: '**********',
      role: 'USER'
    }

    const registerResponse = await fetch(`${BASE_URL}/auth/register`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(registerData)
    })

    const registerResult = await registerResponse.json()
    
    if (registerResult.success) {
      console.log('✅ Registration successful!')
      console.log('👤 User:', registerResult.data.user.email)
      console.log('🔑 Token received:', registerResult.data.token ? 'Yes' : 'No')
      
      // Test 3: Login with the same user
      console.log('\n3️⃣ Testing user login...')
      const loginResponse = await fetch(`${BASE_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: registerData.email,
          password: registerData.password
        })
      })

      const loginResult = await loginResponse.json()
      
      if (loginResult.success) {
        console.log('✅ Login successful!')
        console.log('👤 User:', loginResult.data.user.email)
        console.log('🎭 Role:', loginResult.data.user.role)
        
        // Test 4: Get profile (protected route)
        console.log('\n4️⃣ Testing protected route (get profile)...')
        const profileResponse = await fetch(`${BASE_URL}/auth/profile`, {
          headers: {
            'Authorization': `Bearer ${loginResult.data.token}`
          }
        })

        const profileResult = await profileResponse.json()
        
        if (profileResult.success) {
          console.log('✅ Profile fetch successful!')
          console.log('👤 Profile:', profileResult.data.user.firstName, profileResult.data.user.lastName)
        } else {
          console.log('❌ Profile fetch failed:', profileResult.message)
        }
      } else {
        console.log('❌ Login failed:', loginResult.message)
      }
    } else {
      console.log('❌ Registration failed:', registerResult.message)
      if (registerResult.errors) {
        console.log('Errors:', registerResult.errors)
      }
    }

    console.log('\n🎉 API testing completed!')
    
  } catch (error) {
    console.error('❌ Test failed:', error.message)
    console.log('\n💡 Make sure your server is running on port 5051')
    console.log('💡 Run: npm run dev')
  }
}

// Run the tests
testAPI()
